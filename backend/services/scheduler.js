import cron from "node-cron";
import fetch from "node-fetch";
import Plugin from "../models/Plugin.js";
import PluginRankHistory from "../models/PluginRankHistory.js";
import AddedPlugin from "../models/AddedPlugin.js";
import PluginDownloadData from "../models/PluginDownloadData.js";
import PluginReview from "../models/PluginReview.js";
import PluginKeyword from "../models/PluginKeyword.js";
import PluginKeywordRank from "../models/PluginKeywordRank.js";
import PluginInformation from "../models/PluginInformation.js";

class PluginScheduler {
  constructor() {
    this.isRunning = false;
    this.lastFetchTime = null;
    this.fetchStats = {
      totalPlugins: 0,
      successCount: 0,
      errorCount: 0,
      startTime: null,
      endTime: null,
    };
    this.keywordRefreshStats = {
      totalKeywords: 0,
      successCount: 0,
      errorCount: 0,
      startTime: null,
      endTime: null,
    };
  }

  // Helper function to fetch plugin rank from WordPress API
  async fetchPluginRank(keyword, pluginSlug) {
    try {
      const searchKeyword = keyword;

      console.log(
        `🔍 Scheduler: Searching for keyword "${searchKeyword}" to find rank of plugin "${pluginSlug}"`
      );

      // Get plugin information from database to help with matching
      let pluginInfo = null;
      try {
        pluginInfo = await Plugin.findBySlug(pluginSlug);
        if (pluginInfo) {
          console.log(
            `📋 Scheduler: Plugin info from DB: name="${pluginInfo.name}", slug="${pluginInfo.slug}"`
          );
        }
      } catch (dbError) {
        console.log(
          `⚠️ Scheduler: Could not fetch plugin info from DB: ${dbError.message}`
        );
      }

      // First, try the standard keyword search
      let apiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(
        searchKeyword
      )}&request[per_page]=100`;

      let response = await fetch(apiUrl);
      if (!response.ok) {
        throw new Error(`WordPress API request failed: ${response.status}`);
      }

      let data = await response.json();

      if (!data.plugins || !Array.isArray(data.plugins)) {
        return {
          rank: null,
          totalResults: 0,
          error: "No plugins found in API response",
        };
      }

      console.log(
        `📊 Scheduler: Found ${data.plugins.length} plugins for keyword "${searchKeyword}"`
      );

      // Enhanced plugin matching: try both slug and name matching
      let pluginIndex = data.plugins.findIndex((plugin) => {
        // First try exact slug match
        if (plugin.slug === pluginSlug) {
          return true;
        }

        // If we have plugin info from DB, try name matching
        if (pluginInfo && pluginInfo.name) {
          // Try exact name match
          if (plugin.name === pluginInfo.name) {
            return true;
          }

          // Try case-insensitive name match
          if (plugin.name.toLowerCase() === pluginInfo.name.toLowerCase()) {
            return true;
          }

          // Try partial name match (for cases like "SchedulePress – WordPress Editorial Calendar & Scheduled Blog Posts Plugin")
          if (
            plugin.name.toLowerCase().includes(pluginInfo.name.toLowerCase()) ||
            pluginInfo.name.toLowerCase().includes(plugin.name.toLowerCase())
          ) {
            return true;
          }
        }

        return false;
      });

      if (pluginIndex !== -1) {
        const rank = pluginIndex + 1;
        const foundPlugin = data.plugins[pluginIndex];
        console.log(
          `✅ Scheduler: Plugin "${pluginSlug}" found at position ${rank} for keyword "${searchKeyword}" (matched by: ${
            foundPlugin.slug === pluginSlug ? "slug" : "name"
          }, plugin name: "${foundPlugin.name}")`
        );
        return {
          rank: rank,
          totalResults: data.plugins.length,
          error: null,
        };
      }

      // If not found in first 100 results, try a more specific search for wp-scheduled-posts
      if (pluginSlug === "wp-scheduled-posts") {
        console.log(
          `🔍 Scheduler: Plugin wp-scheduled-posts not found in keyword search, trying SchedulePress search...`
        );

        // Try searching for "SchedulePress" which is the plugin's display name
        const schedulePressUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=SchedulePress&request[per_page]=100`;

        const schedulePressResponse = await fetch(schedulePressUrl);
        if (schedulePressResponse.ok) {
          const schedulePressData = await schedulePressResponse.json();

          if (
            schedulePressData.plugins &&
            Array.isArray(schedulePressData.plugins)
          ) {
            const schedulePressIndex = schedulePressData.plugins.findIndex(
              (plugin) => plugin.slug === "wp-scheduled-posts"
            );

            if (schedulePressIndex !== -1) {
              // Found in SchedulePress search, but we need to estimate its rank for the original keyword
              // Since it wasn't in the keyword search, we'll assign a rank beyond 100
              const estimatedRank = 101 + schedulePressIndex;
              console.log(
                `✅ Scheduler: wp-scheduled-posts found in SchedulePress search at position ${
                  schedulePressIndex + 1
                }, estimated rank for "${searchKeyword}": ${estimatedRank}`
              );
              return {
                rank: estimatedRank,
                totalResults: 100,
                error: null,
              };
            }
          }
        }

        // If still not found, try searching through multiple pages for the keyword
        console.log(
          `🔍 Scheduler: Trying extended search for wp-scheduled-posts with keyword "${searchKeyword}"...`
        );

        for (let page = 2; page <= 3; page++) {
          // Limit to 3 pages for scheduler to avoid too many API calls
          try {
            const extendedUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(
              searchKeyword
            )}&request[page]=${page}&request[per_page]=100`;

            const extendedResponse = await fetch(extendedUrl);
            if (extendedResponse.ok) {
              const extendedData = await extendedResponse.json();

              if (extendedData.plugins && Array.isArray(extendedData.plugins)) {
                // Use the same enhanced matching logic for extended search
                const extendedIndex = extendedData.plugins.findIndex(
                  (plugin) => {
                    // First try exact slug match
                    if (plugin.slug === pluginSlug) {
                      return true;
                    }

                    // If we have plugin info from DB, try name matching
                    if (pluginInfo && pluginInfo.name) {
                      // Try exact name match
                      if (plugin.name === pluginInfo.name) {
                        return true;
                      }

                      // Try case-insensitive name match
                      if (
                        plugin.name.toLowerCase() ===
                        pluginInfo.name.toLowerCase()
                      ) {
                        return true;
                      }

                      // Try partial name match
                      if (
                        plugin.name
                          .toLowerCase()
                          .includes(pluginInfo.name.toLowerCase()) ||
                        pluginInfo.name
                          .toLowerCase()
                          .includes(plugin.name.toLowerCase())
                      ) {
                        return true;
                      }
                    }

                    return false;
                  }
                );

                if (extendedIndex !== -1) {
                  const rank = (page - 1) * 100 + extendedIndex + 1;
                  const foundPlugin = extendedData.plugins[extendedIndex];
                  console.log(
                    `✅ Scheduler: Plugin "${pluginSlug}" found on page ${page} at position ${
                      extendedIndex + 1
                    }, rank: ${rank} (matched by: ${
                      foundPlugin.slug === pluginSlug ? "slug" : "name"
                    }, plugin name: "${foundPlugin.name}")`
                  );
                  return {
                    rank: rank,
                    totalResults: page * 100,
                    error: null,
                  };
                }
              }
            }

            // Add delay between requests
            await new Promise((resolve) => setTimeout(resolve, 300));
          } catch (pageError) {
            console.log(
              `Scheduler: Error searching page ${page}:`,
              pageError.message
            );
          }
        }
      }

      console.log(
        `❌ Scheduler: Plugin "${pluginSlug}" not found in search results for keyword "${searchKeyword}"`
      );
      return {
        rank: null,
        totalResults: data.plugins.length,
        error: "Plugin not found in search results",
      };
    } catch (error) {
      console.error(`Error fetching rank for keyword "${keyword}":`, error);
      return {
        rank: null,
        totalResults: 0,
        error: error.message,
      };
    }
  }

  // Helper function to format date
  formatDate(date = new Date()) {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  // Helper function to calculate keyword occurrences in plugin content
  async calculateKeywordOccurrences(keyword, pluginSlug) {
    try {
      const pluginInfo = await PluginInformation.findOne({
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      });

      if (!pluginInfo || !pluginInfo.sections) {
        return 0;
      }

      let totalOccurrences = 0;
      const keywordLower = keyword.toLowerCase();
      const sections = pluginInfo.sections;

      // Helper function to count occurrences in text
      const countOccurrences = (text) => {
        if (!text || typeof text !== "string") return 0;
        const textLower = text.toLowerCase();
        const regex = new RegExp(
          keywordLower.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          "g"
        );
        const matches = textLower.match(regex);
        return matches ? matches.length : 0;
      };

      // Count in all sections
      Object.values(sections).forEach((sectionContent) => {
        if (typeof sectionContent === "string") {
          totalOccurrences += countOccurrences(sectionContent);
        }
      });

      return totalOccurrences;
    } catch (error) {
      console.error(
        `Error calculating occurrences for keyword "${keyword}":`,
        error
      );
      return 0;
    }
  }

  // Start the scheduler - runs every day at 9 AM GMT+6 (3 AM UTC)
  start() {
    console.log("🕐 Plugin Auto-fetch Scheduler started");

    // Schedule to run every day at 8:00 AM GMT+6 (2:00 AM UTC)
    cron.schedule(
      "0 2 * * *",
      async () => {
        console.log("🚀 Starting scheduled plugin fetch at 8 AM GMT+6...");
        await this.fetchAllPlugins();
      },
      {
        scheduled: true,
        timezone: "UTC",
      }
    );

    // Schedule sequential added plugins refresh every day at 9:00 AM GMT+6 (3:00 AM UTC)
    cron.schedule(
      "0 3 * * *",
      async () => {
        console.log(
          "🔄 Starting scheduled sequential added plugins refresh at 9:00 AM GMT+6..."
        );
        await this.sequentialRefreshAddedPlugins();
      },
      {
        scheduled: true,
        timezone: "UTC",
      }
    );

    // Schedule keyword rank refresh every day at 9:15 AM GMT+6 (3:15 AM UTC)
    cron.schedule(
      "15 3 * * *",
      async () => {
        console.log(
          "🔍 Starting scheduled keyword rank refresh at 9:15 AM GMT+6..."
        );
        await this.refreshAllKeywordRanks();
      },
      {
        scheduled: true,
        timezone: "UTC",
      }
    );

    // Schedule download data fetch every day at 9:25 AM GMT+6 (3:25 AM UTC)
    cron.schedule(
      "25 3 * * *",
      async () => {
        console.log(
          "🚀 Starting scheduled download data fetch at 9:25 AM GMT+6..."
        );
        await this.fetchDownloadDataOnly();
      },
      {
        scheduled: true,
        timezone: "UTC",
      }
    );

    // Schedule dedicated plugin reviews fetch every day at 9:35 AM GMT+6 (3:35 AM UTC)
    cron.schedule(
      "35 3 * * *",
      async () => {
        console.log(
          "📝 Starting scheduled plugin reviews fetch at 9:35 AM GMT+6..."
        );
        await this.fetchAllPluginReviews();
      },
      {
        scheduled: true,
        timezone: "UTC",
      }
    );

    // Schedule added plugins refresh every day at 9:45 AM GMT+6 (3:45 AM UTC)
    cron.schedule(
      "45 3 * * *",
      async () => {
        console.log(
          "🔄 Starting scheduled added plugins refresh at 9:45 AM GMT+6..."
        );
        await this.refreshAllAddedPlugins();
      },
      {
        scheduled: true,
        timezone: "UTC",
      }
    );

    // Also check on startup if last fetch was more than 24 hours ago
    this.checkAndRunIfNeeded();
  }

  // Check if we need to run fetch on startup
  async checkAndRunIfNeeded() {
    try {
      // Check when was the last plugin fetched
      const lastPlugin = await Plugin.findOne().sort({ lastFetched: -1 });

      if (!lastPlugin) {
        console.log(
          "📦 No plugins found in database, scheduling initial fetch..."
        );
        // Run after 5 minutes to allow server to fully start
        setTimeout(() => this.fetchAllPlugins(), 5 * 60 * 1000);
        return;
      }

      const lastFetchTime = new Date(lastPlugin.lastFetched);
      const now = new Date();
      const hoursSinceLastFetch = (now - lastFetchTime) / (1000 * 60 * 60);

      if (hoursSinceLastFetch >= 24) {
        console.log(
          `⏰ Last fetch was ${Math.round(
            hoursSinceLastFetch
          )} hours ago, starting fetch...`
        );
        // Run after 2 minutes to allow server to fully start
        setTimeout(() => this.fetchAllPlugins(), 2 * 60 * 1000);
      } else {
        console.log(
          `✅ Last fetch was ${Math.round(
            hoursSinceLastFetch
          )} hours ago, no fetch needed`
        );
      }
    } catch (error) {
      console.error("❌ Error checking last fetch time:", error);
    }
  }

  // Main fetch function
  async fetchAllPlugins() {
    if (this.isRunning) {
      console.log("⚠️ Plugin fetch already running, skipping...");
      return;
    }

    this.isRunning = true;
    this.fetchStats = {
      totalPlugins: 0,
      successCount: 0,
      errorCount: 0,
      startTime: new Date(),
      endTime: null,
    };

    try {
      console.log("🔄 Starting automatic full plugin fetch...");

      // Use the streaming fetch-all system
      const result = await this.callStreamingFetchAll();

      if (result.success) {
        this.fetchStats.totalPlugins = result.summary.totalProcessedPlugins;
        this.fetchStats.successCount = result.summary.successfulPages;
        this.fetchStats.errorCount = result.summary.failedPages;

        console.log(`✅ Enhanced bulk fetch completed successfully!`);
        console.log(
          `📊 Processed ${result.summary.totalProcessedPlugins} plugins from ${result.summary.successfulPages} pages`
        );
      } else {
        throw new Error(result.message || "Enhanced bulk fetch failed");
      }

      // Update added plugins with new rank data and track rank history
      await this.updateAddedPluginsRanksWithHistory();

      this.fetchStats.endTime = new Date();
      const duration = Math.round(
        (this.fetchStats.endTime - this.fetchStats.startTime) / 1000 / 60
      );

      console.log(`✅ Automatic fetch completed in ${duration} minutes!`);
      console.log(
        `📊 Stats: ${this.fetchStats.successCount} success, ${this.fetchStats.errorCount} errors`
      );

      this.lastFetchTime = new Date();
    } catch (error) {
      console.error("❌ Automatic fetch failed:", error);
      this.fetchStats.endTime = new Date();
    } finally {
      this.isRunning = false;
    }
  }

  // Fetch a single page
  async fetchPage(page, perPage) {
    const apiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${page}&request[per_page]=${perPage}`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`WordPress API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.plugins || !Array.isArray(data.plugins)) {
      throw new Error("Invalid response format from WordPress API");
    }

    const plugins = data.plugins;
    const batchId = `auto_batch_${Date.now()}_${page}`;
    const pluginRankData = [];

    // Process each plugin
    for (let index = 0; index < plugins.length; index++) {
      const plugin = plugins[index];
      const slug = plugin.slug;
      const rank = (page - 1) * perPage + index + 1;
      const title = plugin.name;

      try {
        // Update or create plugin in database (optimized for 5 fields: slug, name, currentRank, short_description, icons)
        await Plugin.findOneAndUpdate(
          { slug: slug },
          {
            slug: slug,
            name: plugin.name,
            currentRank: rank,
            short_description: plugin.short_description || "",
            icons: plugin.icons || {},
            lastFetched: new Date(),
            $inc: { fetchCount: 1 },
          },
          {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true,
          }
        );

        pluginRankData.push({
          slug: slug,
          rank: rank,
          downloads: plugin.downloaded || 0,
          rating: plugin.rating || 0,
          numRatings: plugin.num_ratings || 0,
          version: plugin.version || "",
          lastUpdated: plugin.last_updated || "",
        });

        this.fetchStats.successCount++;
      } catch (pluginError) {
        console.error(`❌ Error updating plugin ${slug}:`, pluginError);
        this.fetchStats.errorCount++;
      }
    }

    // Record rank history for this batch
    try {
      await PluginRankHistory.recordBatchRanks(plugins, batchId);
    } catch (historyError) {
      console.error("❌ Error recording rank history:", historyError);
    }
  }

  // Update ranks for all added plugins with rank history tracking
  async updateAddedPluginsRanksWithHistory() {
    try {
      console.log(
        "🔄 Auto-refetch: Updating added plugins ranks with history tracking..."
      );

      // Get all unique plugin slugs from added plugins
      const addedPluginSlugs = await AddedPlugin.distinct("pluginSlug", {
        isActive: true,
      });

      if (addedPluginSlugs.length === 0) {
        console.log("📝 No added plugins to update");
        return;
      }

      console.log(
        `📊 Found ${addedPluginSlugs.length} added plugins to update`
      );

      // Get current plugin data for these slugs from plugins collection (including icons)
      const plugins = await Plugin.find({ slug: { $in: addedPluginSlugs } });
      const pluginDataMap = new Map();

      plugins.forEach((plugin) => {
        pluginDataMap.set(plugin.slug, {
          currentRank: plugin.currentRank,
          icons: plugin.icons || {},
        });
      });

      // Get all added plugin documents
      const addedPlugins = await AddedPlugin.find({
        pluginSlug: { $in: addedPluginSlugs },
        isActive: true,
      });

      // Import PluginInformation model for updating plugininformations collection
      const PluginInformation = (await import("../models/PluginInformation.js"))
        .default;

      let updatedCount = 0;
      let noChangeCount = 0;

      // Process each added plugin individually for rank history tracking
      for (const addedPlugin of addedPlugins) {
        try {
          const pluginData = pluginDataMap.get(addedPlugin.pluginSlug);
          const pluginsCollectionRank = pluginData?.currentRank || null;
          const pluginsCollectionIcons = pluginData?.icons || {};
          const addedPluginsCurrentRank = addedPlugin.currentRank;

          console.log(
            `🔍 Auto-refetch rank comparison for ${addedPlugin.pluginSlug}:`
          );
          console.log(`   - Plugins collection rank: ${pluginsCollectionRank}`);
          console.log(
            `   - AddedPlugins collection rank: ${addedPluginsCurrentRank}`
          );
          console.log(
            `   - Plugins collection icons:`,
            Object.keys(pluginsCollectionIcons).length > 0
              ? pluginsCollectionIcons
              : "No icons"
          );

          if (
            pluginsCollectionRank !== null &&
            pluginsCollectionRank !== undefined
          ) {
            // Compare the ranks
            if (addedPluginsCurrentRank === pluginsCollectionRank) {
              // Ranks are equal - update icons and timestamp
              console.log(
                `✅ No rank change for ${addedPlugin.pluginSlug} - ranks are equal (${pluginsCollectionRank})`
              );

              // Update icons and lastUpdated timestamp
              addedPlugin.icons = pluginsCollectionIcons;
              addedPlugin.lastUpdated = new Date();
              await addedPlugin.save();

              console.log(
                `🖼️ Icons updated in addedplugins collection for ${addedPlugin.pluginSlug}:`,
                pluginsCollectionIcons
              );

              // Also update plugininformations collection icons
              const pluginInfoDoc = await PluginInformation.findOne({
                pluginSlug: addedPlugin.pluginSlug,
              });

              if (pluginInfoDoc) {
                pluginInfoDoc.icons = pluginsCollectionIcons;
                pluginInfoDoc.fetchedAt = new Date();
                await pluginInfoDoc.save();

                console.log(
                  `🖼️ Icons updated in plugininformations collection for ${addedPlugin.pluginSlug}:`,
                  pluginsCollectionIcons
                );
              }

              noChangeCount++;
            } else {
              // Ranks are different - update addedplugins currentRank, add to rankHistory, and update icons
              console.log(
                `🔄 Rank changed for ${addedPlugin.pluginSlug}: ${addedPluginsCurrentRank} → ${pluginsCollectionRank}`
              );

              await addedPlugin.updateRankWithHistory(pluginsCollectionRank);

              // Update icons after rank update
              addedPlugin.icons = pluginsCollectionIcons;
              await addedPlugin.save();

              console.log(
                `🖼️ Icons updated in addedplugins collection for ${addedPlugin.pluginSlug}:`,
                pluginsCollectionIcons
              );

              // Also update plugininformations collection with new rank and icons
              const pluginInfoDoc = await PluginInformation.findOne({
                pluginSlug: addedPlugin.pluginSlug,
              });

              if (pluginInfoDoc) {
                // Update rank with history tracking
                await pluginInfoDoc.updateRankWithHistory(
                  pluginsCollectionRank
                );

                // Update icons
                pluginInfoDoc.icons = pluginsCollectionIcons;
                pluginInfoDoc.fetchedAt = new Date();
                await pluginInfoDoc.save();

                console.log(
                  `🖼️ Rank and icons updated in plugininformations collection for ${addedPlugin.pluginSlug}:`,
                  { rank: pluginsCollectionRank, icons: pluginsCollectionIcons }
                );
              }

              updatedCount++;

              console.log(
                `✅ Auto-refetch: Rank history and icons updated for ${addedPlugin.pluginSlug}`
              );
            }
          } else {
            // No rank available from plugins collection - just update icons and timestamp
            console.log(
              `⚠️ No rank available for ${addedPlugin.pluginSlug} from plugins collection`
            );

            // Update icons and lastUpdated timestamp
            addedPlugin.icons = pluginsCollectionIcons;
            addedPlugin.lastUpdated = new Date();
            await addedPlugin.save();

            console.log(
              `🖼️ Icons updated in addedplugins collection for ${addedPlugin.pluginSlug}:`,
              pluginsCollectionIcons
            );

            // Also update plugininformations collection icons
            const pluginInfoDoc = await PluginInformation.findOne({
              pluginSlug: addedPlugin.pluginSlug,
            });

            if (pluginInfoDoc) {
              pluginInfoDoc.icons = pluginsCollectionIcons;
              pluginInfoDoc.fetchedAt = new Date();
              await pluginInfoDoc.save();

              console.log(
                `🖼️ Icons updated in plugininformations collection for ${addedPlugin.pluginSlug}:`,
                pluginsCollectionIcons
              );
            }

            noChangeCount++;
          }
        } catch (pluginError) {
          console.error(
            `❌ Error updating rank for ${addedPlugin.pluginSlug}:`,
            pluginError
          );
        }
      }

      console.log(
        `✅ Auto-refetch complete: ${updatedCount} plugins updated, ${noChangeCount} unchanged`
      );
    } catch (error) {
      console.error(
        "❌ Error updating added plugins ranks with history:",
        error
      );
    }
  }

  // Keep the old method for backward compatibility
  async updateAddedPluginsRanks() {
    return this.updateAddedPluginsRanksWithHistory();
  }

  // Get current status
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastFetchTime: this.lastFetchTime,
      fetchStats: this.fetchStats,
    };
  }

  // Call the streaming fetch-all system directly using the same logic as the API endpoint
  async callStreamingFetchAll() {
    try {
      // Import the required modules and functions
      const Plugin = (await import("../models/Plugin.js")).default;
      const fetch = (await import("node-fetch")).default;

      console.log("📡 Using streaming fetch-all system for scheduler...");

      // Import the upsert function from the routes (we'll recreate it here)
      const upsertPluginData = async (pluginData, rank) => {
        try {
          const slug = pluginData.slug;
          if (!slug) {
            throw new Error("Plugin slug is required");
          }

          const updateData = {
            slug: slug,
            name: pluginData.name || slug,
            currentRank: rank > 0 ? rank : null,
            short_description: pluginData.short_description || "",
            icons: pluginData.icons || {},
            lastFetched: new Date(),
            $inc: { fetchCount: 1 },
          };

          const result = await Plugin.findOneAndUpdate(
            { slug: slug },
            updateData,
            {
              upsert: true,
              new: true,
              setDefaultsOnInsert: true,
              runValidators: true,
            }
          );

          return { success: true, plugin: result };
        } catch (error) {
          console.error(`Error upserting plugin ${pluginData.slug}:`, error);
          return { success: false, error: error.message };
        }
      };

      // Get total count first
      const countUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[per_page]=1`;
      const countResponse = await fetch(countUrl);

      if (!countResponse.ok) {
        throw new Error(`WordPress API error: ${countResponse.status}`);
      }

      const countData = await countResponse.json();
      const totalPlugins = countData.info?.results || 0;
      const perPage = 100;
      const totalPages =
        countData.info?.pages || Math.ceil(totalPlugins / perPage);

      console.log(
        `📊 Total plugins: ${totalPlugins}, Total pages: ${totalPages}`
      );

      let successfulPages = 0;
      let failedPages = 0;
      let totalProcessedPlugins = 0;
      const errors = [];
      const startTime = Date.now();

      // Process pages in batches (same as the API endpoint)
      const batchSize = 5;
      for (
        let batchStart = 1;
        batchStart <= totalPages;
        batchStart += batchSize
      ) {
        const batchEnd = Math.min(batchStart + batchSize - 1, totalPages);
        const batchPromises = [];

        for (let page = batchStart; page <= batchEnd; page++) {
          batchPromises.push(this.fetchPageEnhanced(page, perPage, 3));
        }

        const batchResults = await Promise.all(batchPromises);

        for (const result of batchResults) {
          if (result.success) {
            try {
              const plugins = result.data.plugins || [];

              for (let index = 0; index < plugins.length; index++) {
                const plugin = plugins[index];
                const slug = plugin.slug;
                const rank = (result.page - 1) * perPage + index + 1;

                // Store or update plugin in database using enhanced upsert
                const upsertResult = await upsertPluginData(plugin, rank);
                if (!upsertResult.success) {
                  console.error(
                    `Failed to upsert plugin ${slug}:`,
                    upsertResult.error
                  );
                }
              }

              successfulPages++;
              totalProcessedPlugins += plugins.length;

              console.log(
                `✅ Scheduler: Page ${result.page}/${totalPages} completed (${plugins.length} plugins)`
              );
            } catch (processError) {
              console.error(
                `Error processing page ${result.page}:`,
                processError
              );
              failedPages++;
              errors.push(`Page ${result.page}: ${processError.message}`);
            }
          } else {
            failedPages++;
            console.warn(
              `⚠️ Scheduler: Skipping failed page ${result.page}: ${result.error}`
            );
            errors.push(`Page ${result.page}: ${result.error}`);
          }
        }

        // Add delay between batches
        if (batchEnd < totalPages) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        // Log progress every 10 batches
        if (batchStart % 50 === 1) {
          const progress = Math.round((batchEnd / totalPages) * 100);
          console.log(
            `📊 Scheduler progress: ${progress}% (${totalProcessedPlugins} plugins processed)`
          );
        }
      }

      const endTime = Date.now();
      const totalDuration = endTime - startTime;

      return {
        success: true,
        message: `Scheduler completed: ${totalProcessedPlugins} plugins processed from ${successfulPages} pages.`,
        summary: {
          totalProcessedPlugins,
          successfulPages,
          failedPages,
          totalPages,
          totalPlugins,
          totalDuration,
          averagePluginsPerSecond: Math.round(
            totalProcessedPlugins / (totalDuration / 1000)
          ),
          successRate: Math.round((successfulPages / totalPages) * 100),
          errors: errors.slice(0, 10),
        },
      };
    } catch (error) {
      console.error("❌ Scheduler fetch-all error:", error);
      return {
        success: false,
        message: "Scheduler failed to fetch plugins",
        error: error.message,
      };
    }
  }

  // Enhanced fetch page method
  async fetchPageEnhanced(page, perPage, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutDuration = 15000 + attempt * 5000;
        const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

        const response = await fetch(
          `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${page}&request[per_page]=${perPage}`,
          {
            signal: controller.signal,
            headers: {
              "User-Agent": "PluginSight-Scheduler/1.0",
              Accept: "application/json",
            },
          }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data || !data.plugins || !Array.isArray(data.plugins)) {
          throw new Error("Invalid response format");
        }

        return { page, data, success: true };
      } catch (error) {
        if (attempt === maxRetries) {
          return { page, error: error.message, success: false };
        }

        const waitTime = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      }
    }
  }

  // Fetch download data only for all added plugins
  async fetchDownloadDataOnly() {
    if (this.isRunning) {
      console.log(
        "⚠️ Plugin fetch already running, skipping download data fetch..."
      );
      return;
    }

    console.log("🔄 Starting automatic download data fetch...");

    try {
      // Get all added plugins
      const addedPlugins = await AddedPlugin.find({ isActive: true });

      if (addedPlugins.length === 0) {
        console.log("📝 No added plugins found for download data fetch");
        return;
      }

      console.log(
        `📊 Found ${addedPlugins.length} added plugins to fetch download data`
      );

      let downloadSuccessCount = 0;
      let downloadErrorCount = 0;

      // Fetch download data for each plugin
      for (const plugin of addedPlugins) {
        try {
          console.log(`Fetching download data for ${plugin.pluginSlug}...`);

          const apiUrl = `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${plugin.pluginSlug}`;
          const response = await fetch(apiUrl);

          if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
          }

          const downloadData = await response.json();

          if (downloadData && typeof downloadData === "object") {
            // Store complete download data in single entry
            await PluginDownloadData.upsertDownloadData(
              plugin.pluginSlug,
              plugin.displayName || plugin.pluginName,
              downloadData
            );
            downloadSuccessCount++;
            console.log(`✅ Download data updated for ${plugin.pluginSlug}`);
          }
        } catch (downloadError) {
          console.error(
            `❌ Error fetching download data for ${plugin.pluginSlug}:`,
            downloadError
          );
          downloadErrorCount++;
        }

        // Add delay between requests to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      console.log(`✅ Download data fetch completed!`);
      console.log(
        `📊 Download Data - Success: ${downloadSuccessCount}, Errors: ${downloadErrorCount}`
      );
    } catch (error) {
      console.error("❌ Error in download data fetch:", error);
    }
  }

  // Fetch download data and reviews for all added plugins
  async fetchDownloadDataAndReviews() {
    if (this.isRunning) {
      console.log(
        "⚠️ Plugin fetch already running, skipping download data and reviews fetch..."
      );
      return;
    }

    console.log("🔄 Starting automatic download data and reviews fetch...");

    try {
      // Get all added plugins
      const addedPlugins = await AddedPlugin.find({ isActive: true });

      if (addedPlugins.length === 0) {
        console.log(
          "📝 No added plugins found for download data and reviews fetch"
        );
        return;
      }

      console.log(
        `📊 Found ${addedPlugins.length} added plugins to fetch download data and reviews`
      );

      let downloadSuccessCount = 0;
      let downloadErrorCount = 0;
      let reviewSuccessCount = 0;
      let reviewErrorCount = 0;

      // Fetch download data for each plugin
      for (const plugin of addedPlugins) {
        try {
          console.log(`Fetching download data for ${plugin.pluginSlug}...`);

          const apiUrl = `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${plugin.pluginSlug}`;
          const response = await fetch(apiUrl);

          if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
          }

          const downloadData = await response.json();

          if (downloadData && typeof downloadData === "object") {
            // Store complete download data in single entry
            await PluginDownloadData.upsertDownloadData(
              plugin.pluginSlug,
              plugin.displayName || plugin.pluginName,
              downloadData
            );
            downloadSuccessCount++;
            console.log(`✅ Download data updated for ${plugin.pluginSlug}`);
          }
        } catch (downloadError) {
          console.error(
            `❌ Error fetching download data for ${plugin.pluginSlug}:`,
            downloadError
          );
          downloadErrorCount++;
        }

        // Add delay between requests to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // Fetch reviews for each plugin
      for (const plugin of addedPlugins) {
        try {
          console.log(`Fetching reviews for ${plugin.pluginSlug}...`);

          const reviewsUrl = `https://wordpress.org/plugins/${plugin.pluginSlug}/reviews/feed/`;
          const reviewsResponse = await fetch(reviewsUrl);

          if (reviewsResponse.ok) {
            const rssText = await reviewsResponse.text();

            if (rssText && rssText.trim().length > 0) {
              await this.parseAndStoreReviews(rssText, plugin);
              reviewSuccessCount++;
              console.log(`✅ Reviews updated for ${plugin.pluginSlug}`);
            }
          } else {
            console.warn(
              `⚠️ Reviews not available for ${plugin.pluginSlug}: ${reviewsResponse.status}`
            );
          }
        } catch (reviewError) {
          console.error(
            `❌ Error fetching reviews for ${plugin.pluginSlug}:`,
            reviewError
          );
          reviewErrorCount++;
        }

        // Add delay between requests to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

      console.log(`✅ Download data and reviews fetch completed!`);
      console.log(
        `📊 Download Data - Success: ${downloadSuccessCount}, Errors: ${downloadErrorCount}`
      );
      console.log(
        `📊 Reviews - Success: ${reviewSuccessCount}, Errors: ${reviewErrorCount}`
      );
    } catch (error) {
      console.error("❌ Error in download data and reviews fetch:", error);
    }
  }

  // Parse and store reviews from RSS feed
  async parseAndStoreReviews(rssText, plugin) {
    try {
      console.log(`Parsing RSS reviews for plugin: ${plugin.pluginSlug}`);

      // Simple RSS parsing to extract reviews
      const reviewsData = {};
      const itemRegex = /<item>(.*?)<\/item>/gs;
      let match;
      let itemCount = 0;

      while ((match = itemRegex.exec(rssText)) !== null && itemCount < 50) {
        const item = match[1];
        itemCount++;

        try {
          // Extract basic fields
          const titleMatch = item.match(
            /<title><!\[CDATA\[(.*?)\]\]><\/title>/
          );
          const linkMatch = item.match(/<link>(.*?)<\/link>/);
          const pubDateMatch = item.match(/<pubDate>(.*?)<\/pubDate>/);
          const descriptionMatch = item.match(
            /<description><!\[CDATA\[(.*?)\]\]><\/description>/
          );
          const guidMatch = item.match(/<guid.*?>(.*?)<\/guid>/);

          if (
            !titleMatch ||
            !linkMatch ||
            !pubDateMatch ||
            !descriptionMatch ||
            !guidMatch
          ) {
            continue;
          }

          const title = titleMatch[1].trim();
          const link = linkMatch[1].trim();
          const pubDate = new Date(pubDateMatch[1].trim());
          const description = descriptionMatch[1].trim();
          const guid = guidMatch[1].trim();

          // Extract rating from title (★★★★★ format)
          const ratingMatch = title.match(/★+/);
          const rating = ratingMatch ? ratingMatch[0].length : 0;

          // Extract author from description
          const authorMatch = description.match(/By\s+([^,\n]+)/i);
          const author = authorMatch ? authorMatch[1].trim() : "Anonymous";

          // Clean content - remove rating info and get main content
          let content = description
            .replace(/^.*?Rating:\s*\d+\s*stars?\s*/i, "")
            .replace(/^.*?Replies:\s*\d+\s*/i, "")
            .replace(/By\s+[^,\n]+,?\s*/i, "")
            .trim();

          // Use guid as unique identifier with fallback
          let reviewId = null;
          if (guid && guid.trim()) {
            reviewId = guid.split("/").pop() || guid;
          } else if (link && link.trim()) {
            reviewId = link.split("/").pop() || link;
          }

          // Generate proper reviewId if still null or empty
          if (!reviewId || !reviewId.trim()) {
            // Use the proper format: pluginSlug-author-datePublished
            const cleanAuthor = (author || "anonymous")
              .toLowerCase()
              .replace(/[^a-z0-9]/g, "")
              .substring(0, 15);
            const dateStr = pubDate.toISOString().split("T")[0];
            const timestamp = Date.now().toString().slice(-6);
            reviewId = `${plugin.pluginSlug}-${cleanAuthor}-${dateStr}-${timestamp}`;
            console.log(
              `⚠️ Generated reviewId for ${plugin.pluginSlug}: ${reviewId}`
            );
          }

          // Clean title (remove rating stars and rating text)
          const cleanTitle = title
            .replace(/^★+\s*/, "")
            .replace(/\s*\(\d+\s+stars?\)$/i, "");

          const reviewResult = await PluginReview.upsertReview({
            pluginSlug: plugin.pluginSlug,
            pluginName: plugin.displayName || plugin.pluginName,
            reviewId: reviewId,
            title: cleanTitle,
            content,
            rating,
            author,
            date: pubDate,
            reviewUrl: link,
            originalData: item,
          });

          console.log(
            `Processed review ${reviewId} for ${plugin.pluginSlug}: ${
              reviewResult ? "Success" : "Failed"
            }`
          );
        } catch (reviewError) {
          console.error(
            `Error processing review for ${plugin.pluginSlug}:`,
            reviewError
          );
        }
      }

      console.log(`Processed ${itemCount} reviews for ${plugin.pluginSlug}`);
    } catch (error) {
      console.error(`Error parsing reviews for ${plugin.pluginSlug}:`, error);
      throw error;
    }
  }

  // Refresh all keyword ranks for all users
  async refreshAllKeywordRanks() {
    try {
      console.log("🔍 Starting automatic keyword rank refresh...");

      this.keywordRefreshStats.startTime = new Date();
      this.keywordRefreshStats.totalKeywords = 0;
      this.keywordRefreshStats.successCount = 0;
      this.keywordRefreshStats.errorCount = 0;

      // Get all active keywords from pluginkeywordranks collection
      const allKeywords = await PluginKeywordRank.find({ isActive: true });

      if (allKeywords.length === 0) {
        console.log("✅ No keywords found to refresh");
        return;
      }

      console.log(`📊 Found ${allKeywords.length} keywords to process`);

      const currentDate = this.formatDate();
      let totalKeywordsProcessed = 0;

      // Process each keyword record
      for (const keywordRecord of allKeywords) {
        try {
          totalKeywordsProcessed++;
          this.keywordRefreshStats.totalKeywords++;

          const keyword = keywordRecord.keyword;
          const pluginSlug = keywordRecord.pluginSlug;

          console.log(
            `🔍 Refreshing rank for keyword "${keyword}" in plugin ${pluginSlug}`
          );

          // Fetch current rank from WordPress API
          const rankResult = await this.fetchPluginRank(keyword, pluginSlug);

          // Calculate keyword occurrences
          const occurrences = await this.calculateKeywordOccurrences(
            keyword,
            pluginSlug
          );

          if (rankResult.rank !== null) {
            // Update keyword rank and occurrences in PluginKeywordRank collection
            await PluginKeywordRank.upsertKeywordRank(
              pluginSlug,
              keywordRecord.pluginName || pluginSlug,
              keyword,
              rankResult.rank,
              currentDate,
              keywordRecord.source || "default",
              occurrences
            );

            this.keywordRefreshStats.successCount++;
            console.log(
              `✅ Updated rank for "${keyword}" in ${pluginSlug}: rank ${rankResult.rank}, occurrences ${occurrences}`
            );
          } else {
            this.keywordRefreshStats.errorCount++;
            console.log(
              `❌ Failed to get rank for "${keyword}" in ${pluginSlug}: ${rankResult.error}`
            );
          }

          // Add small delay to avoid overwhelming the API
          await new Promise((resolve) => setTimeout(resolve, 100));
        } catch (error) {
          this.keywordRefreshStats.errorCount++;
          console.error(
            `❌ Error refreshing keyword "${keyword}" for plugin ${pluginSlug}:`,
            error
          );
        }
      }

      this.keywordRefreshStats.endTime = new Date();
      const duration = Math.round(
        (this.keywordRefreshStats.endTime -
          this.keywordRefreshStats.startTime) /
          1000 /
          60
      );

      console.log(`✅ Keyword rank refresh completed in ${duration} minutes!`);
      console.log(
        `📊 Stats: ${this.keywordRefreshStats.successCount} success, ${this.keywordRefreshStats.errorCount} errors out of ${this.keywordRefreshStats.totalKeywords} total keywords`
      );
    } catch (error) {
      console.error("❌ Keyword rank refresh failed:", error);
      this.keywordRefreshStats.endTime = new Date();
    }
  }

  // Fetch reviews for all added plugins (dedicated method for scheduled reviews)
  async fetchAllPluginReviews() {
    console.log("📝 Starting dedicated plugin reviews fetch...");

    try {
      // Get all added plugins
      const addedPlugins = await AddedPlugin.find({ isActive: true });

      if (addedPlugins.length === 0) {
        console.log("📝 No added plugins found for reviews fetch");
        return;
      }

      console.log(
        `📊 Found ${addedPlugins.length} added plugins to fetch reviews`
      );

      let reviewSuccessCount = 0;
      let reviewErrorCount = 0;

      // Fetch reviews for each plugin
      for (const plugin of addedPlugins) {
        try {
          console.log(`Fetching reviews for ${plugin.pluginSlug}...`);

          const reviewsUrl = `https://wordpress.org/plugins/${plugin.pluginSlug}/reviews/feed/`;
          const reviewsResponse = await fetch(reviewsUrl);

          if (reviewsResponse.ok) {
            const rssText = await reviewsResponse.text();

            if (rssText && rssText.trim().length > 0) {
              await this.parseAndStoreReviews(rssText, plugin);
              reviewSuccessCount++;
              console.log(`✅ Reviews updated for ${plugin.pluginSlug}`);
            }
          } else {
            console.warn(
              `⚠️ Reviews not available for ${plugin.pluginSlug}: ${reviewsResponse.status}`
            );
          }
        } catch (reviewError) {
          console.error(
            `❌ Error fetching reviews for ${plugin.pluginSlug}:`,
            reviewError
          );
          reviewErrorCount++;
        }

        // Add delay between requests to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 200));
      }

      console.log(`✅ Plugin reviews fetch completed!`);
      console.log(
        `📊 Reviews - Success: ${reviewSuccessCount}, Errors: ${reviewErrorCount}`
      );
    } catch (error) {
      console.error("❌ Error in plugin reviews fetch:", error);
    }
  }

  // Refresh all added plugins with fresh data from WordPress API and current rank/icons from plugins collection
  async refreshAllAddedPlugins() {
    try {
      console.log("🔄 Starting refresh of all added plugins...");

      // Get all unique plugin slugs from added plugins
      const addedPluginSlugs = await AddedPlugin.distinct("pluginSlug", {
        isActive: true,
      });

      if (addedPluginSlugs.length === 0) {
        console.log("📝 No added plugins to refresh");
        return;
      }

      console.log(
        `📊 Found ${addedPluginSlugs.length} added plugins to refresh`
      );

      let successCount = 0;
      let errorCount = 0;

      // Process each plugin individually
      for (const slug of addedPluginSlugs) {
        try {
          console.log(`🔄 Refreshing plugin: ${slug}`);

          // Fetch fresh plugin data from WordPress API
          const pluginApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${slug}&request[fields][icons]=true`;
          const pluginResponse = await fetch(pluginApiUrl);

          if (!pluginResponse.ok) {
            console.warn(
              `⚠️ WordPress API error for ${slug}: ${pluginResponse.status}`
            );
            errorCount++;
            continue;
          }

          const pluginData = await pluginResponse.json();
          const displayName = pluginData.name.split(/[-–:]|&#8211;/)[0].trim();

          // Update plugin data in plugins collection
          await Plugin.findOneAndUpdate(
            { slug: slug },
            {
              slug: slug,
              name: pluginData.name,
              short_description: pluginData.short_description || "",
              icons: pluginData.icons || {},
              lastFetched: new Date(),
              $inc: { fetchCount: 1 },
            },
            { upsert: true, new: true }
          );

          // Get current rank and icons from plugins collection
          const pluginFromDb = await Plugin.findOne({ slug: slug });
          const pluginsCollectionRank = pluginFromDb?.currentRank || null;
          const pluginsCollectionIcons = pluginFromDb?.icons || {};

          // Get all added plugin documents for this slug
          const addedPlugins = await AddedPlugin.find({
            pluginSlug: slug,
            isActive: true,
          });

          // Update each added plugin document
          for (const addedPlugin of addedPlugins) {
            const addedPluginsCurrentRank = addedPlugin.currentRank;

            console.log(`🔍 Rank comparison for ${slug}:`);
            console.log(
              `   - Plugins collection rank: ${pluginsCollectionRank}`
            );
            console.log(
              `   - AddedPlugins collection rank: ${addedPluginsCurrentRank}`
            );

            if (pluginsCollectionRank !== null) {
              // Compare the ranks
              if (addedPluginsCurrentRank === pluginsCollectionRank) {
                // Ranks are equal - update icons and lastUpdated timestamp
                console.log(
                  `✅ No rank change for ${slug} - ranks are equal (${pluginsCollectionRank})`
                );

                addedPlugin.icons = pluginsCollectionIcons;
                addedPlugin.lastUpdated = new Date();
                await addedPlugin.save();
              } else {
                // Ranks are different - update rank history and icons
                console.log(
                  `🔄 Rank changed for ${slug}: ${addedPluginsCurrentRank} → ${pluginsCollectionRank}`
                );

                await addedPlugin.updateRankWithHistory(pluginsCollectionRank);
                addedPlugin.icons = pluginsCollectionIcons;
                await addedPlugin.save();
              }
            } else {
              // No rank available - just update icons and timestamp
              console.log(
                `⚠️ No rank available for ${slug} from plugins collection`
              );

              addedPlugin.icons = pluginsCollectionIcons;
              addedPlugin.lastUpdated = new Date();
              await addedPlugin.save();
            }
          }

          successCount++;
          console.log(`✅ Plugin ${slug} refreshed successfully`);

          // Add delay between requests to be respectful to the API
          await new Promise((resolve) => setTimeout(resolve, 200));
        } catch (pluginError) {
          console.error(`❌ Error refreshing plugin ${slug}:`, pluginError);
          errorCount++;
        }
      }

      console.log(`✅ Added plugins refresh completed!`);
      console.log(
        `📊 Refresh - Success: ${successCount}, Errors: ${errorCount}`
      );
    } catch (error) {
      console.error("❌ Error in added plugins refresh:", error);
    }
  }

  // Manual trigger (for testing or admin use)
  async triggerManualFetch() {
    if (this.isRunning) {
      throw new Error("Fetch already running");
    }

    console.log("🔧 Manual fetch triggered");
    return this.fetchAllPlugins();
  }

  // Manual trigger for keyword refresh (for testing or admin use)
  async triggerManualKeywordRefresh() {
    console.log("🔧 Manual keyword refresh triggered");
    return this.refreshAllKeywordRanks();
  }

  // Manual trigger for plugin reviews fetch (for testing or admin use)
  async triggerManualReviewsFetch() {
    console.log("🔧 Manual plugin reviews fetch triggered");
    return this.fetchAllPluginReviews();
  }

  // Sequential refresh of added plugins (same logic as frontend refresh button)
  async sequentialRefreshAddedPlugins() {
    try {
      console.log("🔄 Starting sequential refresh of added plugins...");

      // Get all unique plugin slugs from added plugins (same as frontend logic)
      const addedPluginSlugs = await AddedPlugin.distinct("pluginSlug", {
        isActive: true,
      });

      if (addedPluginSlugs.length === 0) {
        console.log("📝 No added plugins to refresh");
        return;
      }

      console.log(
        `📊 Found ${addedPluginSlugs.length} added plugins to refresh sequentially`
      );

      // Import the refresh endpoint logic
      const express = require("express");
      const app = express();

      // Process each plugin sequentially using the same API endpoint logic
      for (const slug of addedPluginSlugs) {
        try {
          console.log(`🔄 Sequentially refreshing plugin: ${slug}`);

          // Call the same refresh logic that the API endpoint uses
          // This mimics the POST /api/plugins/added/:slug/refresh endpoint
          await this.refreshSingleAddedPlugin(slug);

          console.log(`✅ Plugin ${slug} refreshed successfully`);
        } catch (pluginError) {
          console.error(`❌ Error refreshing plugin ${slug}:`, pluginError);
        }
      }

      console.log(`✅ Sequential added plugins refresh completed!`);
    } catch (error) {
      console.error("❌ Error in sequential added plugins refresh:", error);
    }
  }

  // Helper method to refresh a single added plugin (mimics API endpoint logic)
  async refreshSingleAddedPlugin(slug) {
    try {
      // Get added plugin from any admin/superadmin user
      const User = (await import("../models/User.js")).default;
      const adminUsers = await User.find({
        role: { $in: ["admin", "superadmin"] },
        isActive: true,
      }).select("_id");

      const adminUserIds = adminUsers.map((user) => user._id);

      const addedPlugin = await AddedPlugin.findOne({
        userId: { $in: adminUserIds },
        pluginSlug: slug,
        isActive: true,
      });

      if (!addedPlugin) {
        console.warn(`⚠️ Added plugin not found: ${slug}`);
        return;
      }

      // Fetch fresh plugin data from WordPress API
      const pluginApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${slug}&request[fields][icons]=true`;
      const pluginResponse = await fetch(pluginApiUrl);

      if (!pluginResponse.ok) {
        console.warn(
          `⚠️ WordPress API error for ${slug}: ${pluginResponse.status}`
        );
        return;
      }

      const pluginData = await pluginResponse.json();
      const displayName = pluginData.name.split(/[-–:]|&#8211;/)[0].trim();

      // Update plugin data in plugins collection
      await Plugin.findOneAndUpdate(
        { slug: slug },
        {
          name: pluginData.name,
          displayName: displayName,
          version: pluginData.version,
          author: pluginData.author,
          icons: pluginData.icons || {},
          lastFetched: new Date(),
        },
        { upsert: true }
      );

      // Update added plugin with fresh data
      addedPlugin.pluginName = pluginData.name;
      addedPlugin.displayName = displayName;
      addedPlugin.version = pluginData.version;
      addedPlugin.icons = pluginData.icons || {};
      addedPlugin.lastUpdated = new Date();

      await addedPlugin.save();

      console.log(`✅ Single plugin refresh completed for: ${slug}`);
    } catch (error) {
      console.error(`❌ Error in single plugin refresh for ${slug}:`, error);
      throw error;
    }
  }
}

// Create singleton instance
const pluginScheduler = new PluginScheduler();

export default pluginScheduler;
