import mongoose from "mongoose";

const pluginKeywordRankSchema = new mongoose.Schema(
  {
    pluginSlug: {
      type: String,
      required: [true, "Plugin slug is required"],
      trim: true,
      lowercase: true,
    },
    pluginName: {
      type: String,
      required: [true, "Plugin name is required"],
      trim: true,
    },
    keyword: {
      type: String,
      required: [true, "Keyword is required"],
      trim: true,
    },
    // Source of the keyword (default, manual)
    source: {
      type: String,
      enum: ["default", "manual", "migrated"],
      default: "default",
    },
    // Store rank history as an object with dates as keys
    rankHistory: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    // Current rank for quick access
    currentRank: {
      type: Number,
      default: null,
    },
    // Latest date for quick access
    latestDate: {
      type: String, // Format: dd-mm-yyyy
      default: null,
    },
    // Current occurrences count for quick access
    currentOccurrences: {
      type: Number,
      default: 0,
    },
    // Metadata
    fetchedAt: {
      type: Date,
      default: Date.now,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes for efficient queries
pluginKeywordRankSchema.index({ pluginSlug: 1, keyword: 1 }, { unique: true });
pluginKeywordRankSchema.index({ pluginSlug: 1, fetchedAt: -1 });
pluginKeywordRankSchema.index({ keyword: 1, fetchedAt: -1 });
pluginKeywordRankSchema.index({ fetchedAt: -1 });

// Static method to upsert keyword rank data with history
pluginKeywordRankSchema.statics.upsertKeywordRank = async function (
  pluginSlug,
  pluginName,
  keyword,
  rank,
  date,
  source = "default",
  occurrences = 0
) {
  // Find existing record or create new one
  let keywordRankRecord = await this.findOne({
    pluginSlug: pluginSlug.toLowerCase().trim(),
    keyword: keyword.toLowerCase().trim(),
  });

  if (!keywordRankRecord) {
    keywordRankRecord = new this({
      pluginSlug: pluginSlug.toLowerCase().trim(),
      pluginName,
      keyword: keyword.toLowerCase().trim(),
      source: source,
      rankHistory: {},
      currentRank: rank,
      latestDate: date,
      currentOccurrences: occurrences,
      fetchedAt: new Date(),
      isActive: true,
    });
  } else {
    // Update source if it's different (in case keyword source changed)
    if (keywordRankRecord.source !== source) {
      keywordRankRecord.source = source;
    }
  }

  // Update rank history only if rank is not null
  if (!keywordRankRecord.rankHistory) {
    keywordRankRecord.rankHistory = {};
  }

  // Only add to history if rank is not null and (rank has changed or it's a new date)
  if (rank !== null && rank !== undefined) {
    const existingRankForDate = keywordRankRecord.rankHistory[date];
    if (!existingRankForDate || existingRankForDate.rank !== rank) {
      keywordRankRecord.rankHistory[date] = {
        rank: rank,
        fetchedAt: new Date(),
      };

      // Update current rank and latest date
      keywordRankRecord.currentRank = rank;
      keywordRankRecord.latestDate = date;
      keywordRankRecord.fetchedAt = new Date();
    }
  } else {
    // If rank is null but this is a new keyword, still update the fetchedAt timestamp
    keywordRankRecord.fetchedAt = new Date();
  }

  // Always update occurrences (they can change even if rank doesn't)
  keywordRankRecord.currentOccurrences = occurrences;

  return keywordRankRecord.save();
};

// Static method to get keyword rank record
pluginKeywordRankSchema.statics.getKeywordRankRecord = function (
  pluginSlug,
  keyword
) {
  return this.findOne({
    pluginSlug: pluginSlug.toLowerCase().trim(),
    keyword: keyword.toLowerCase().trim(),
    isActive: true,
  });
};

// Static method to get all keyword ranks for a plugin
pluginKeywordRankSchema.statics.getPluginKeywordRanks = function (pluginSlug) {
  return this.find({
    pluginSlug: pluginSlug.toLowerCase().trim(),
    isActive: true,
  }).sort({ keyword: 1 });
};

// Static method to get keywords for user's added plugins from pluginkeywordranks collection
pluginKeywordRankSchema.statics.getUserKeywordsFromRanks = async function (
  userId,
  pluginSlug = null
) {
  try {
    console.log(
      `getUserKeywordsFromRanks called for user ${userId}, plugin: ${
        pluginSlug || "all"
      }`
    );

    // Dynamic import to avoid circular dependency
    const { default: AddedPlugin } = await import("./AddedPlugin.js");

    // Get user's added plugins
    let addedPluginsQuery = { userId, isActive: true };
    if (pluginSlug) {
      addedPluginsQuery.pluginSlug = pluginSlug.toLowerCase();
    }

    const addedPlugins = await AddedPlugin.find(addedPluginsQuery).select(
      "pluginSlug"
    );

    console.log(`Found ${addedPlugins.length} added plugins for user`);

    if (addedPlugins.length === 0) {
      console.log("No added plugins found for user, returning empty array");
      return [];
    }

    // Get plugin slugs
    const pluginSlugs = addedPlugins.map((plugin) => plugin.pluginSlug);
    console.log("Plugin slugs:", pluginSlugs);

    // Get keyword rank records for these plugins
    let keywordRankRecords = [];
    try {
      keywordRankRecords = await this.find({
        pluginSlug: { $in: pluginSlugs },
        isActive: true,
      }).sort({ pluginSlug: 1, keyword: 1 });
    } catch (dbError) {
      console.error("Error fetching keyword rank records:", dbError);
      return [];
    }

    console.log(`Found ${keywordRankRecords.length} keyword rank records`);

    if (keywordRankRecords.length === 0) {
      console.log("No keyword rank records found, returning empty array");
      return [];
    }

    // Process keyword rank records into the expected format
    const allKeywords = [];

    for (const record of keywordRankRecords) {
      let latestRank = null;
      let previousRank = null;
      let rankChange = null;
      let lastChecked = null;

      try {
        if (record.rankHistory && typeof record.rankHistory === "object") {
          // Process rank history
          const historyEntries = Object.keys(record.rankHistory)
            .map((date) => ({
              date,
              rank: record.rankHistory[date].rank,
              fetchedAt: record.rankHistory[date].fetchedAt,
            }))
            .sort((a, b) => {
              const dateA = new Date(a.date.split("-").reverse().join("-"));
              const dateB = new Date(b.date.split("-").reverse().join("-"));
              return dateB - dateA;
            })
            .slice(0, 2);

          if (historyEntries.length > 0) {
            latestRank = historyEntries[0].rank;
            lastChecked = historyEntries[0].fetchedAt;

            if (historyEntries.length > 1) {
              previousRank = historyEntries[1].rank;
              if (latestRank && previousRank) {
                rankChange = previousRank - latestRank; // Positive means improvement (rank went down in number)
              }
            }
          }
        }

        // Use current rank if no history available
        if (latestRank === null && record.currentRank !== null) {
          latestRank = record.currentRank;
          lastChecked = record.fetchedAt;
        }
      } catch (rankError) {
        console.error(
          `Error processing rank data for keyword ${record.keyword} in plugin ${record.pluginSlug}:`,
          rankError
        );
      }

      allKeywords.push({
        _id: `${record.pluginSlug}-${record.keyword}`,
        keyword: record.keyword,
        pluginSlug: record.pluginSlug,
        pluginName: record.pluginName,
        source: record.source || "default", // Use actual source from database
        addedAt: record.createdAt, // Tracked column - from pluginkeywordranks createdAt
        updatedAt: record.updatedAt, // Updated column - from pluginkeywordranks updatedAt
        type: "plugin_tags", // Consistent with pluginkeywords collection
        isActive: record.isActive !== false,
        latestRank: latestRank,
        previousRank: previousRank,
        rankChange: rankChange,
        lastChecked: lastChecked,
        occurrences: record.currentOccurrences || 0, // Include occurrences from database
      });
    }

    console.log(
      `Returning ${allKeywords.length} keywords from getUserKeywordsFromRanks`
    );
    return allKeywords;
  } catch (error) {
    console.error("Error in getUserKeywordsFromRanks:", error);
    console.error("Error stack:", error.stack);
    throw error;
  }
};

// Static method to add keywords from plugin tags directly to pluginkeywordranks collection
pluginKeywordRankSchema.statics.addKeywordsFromTags = async function (
  pluginSlug,
  pluginName,
  tags = {}
) {
  if (!tags || typeof tags !== "object") {
    return null;
  }

  console.log(
    `Adding keywords from tags for plugin ${pluginSlug}:`,
    Object.keys(tags)
  );

  const currentDate = new Date().toLocaleDateString("en-GB"); // Format: dd-mm-yyyy
  const addedKeywords = [];

  // Add each tag as a keyword directly to pluginkeywordranks collection
  for (const tag of Object.keys(tags)) {
    if (tag && tag.trim()) {
      try {
        const keyword = tag.trim();
        const keywordKey = keyword.toLowerCase().trim();

        // Check if keyword already exists
        const existingRecord = await this.findOne({
          pluginSlug: pluginSlug.toLowerCase().trim(),
          keyword: keywordKey,
        });

        if (!existingRecord) {
          // Create new keyword rank record
          const keywordRankRecord = new this({
            pluginSlug: pluginSlug.toLowerCase().trim(),
            pluginName,
            keyword: keywordKey,
            source: "default",
            rankHistory: {},
            currentRank: null, // Will be populated when rank is fetched
            latestDate: currentDate,
            currentOccurrences: 0, // Will be populated when rank is fetched
            fetchedAt: new Date(),
            isActive: true,
          });

          await keywordRankRecord.save();
          addedKeywords.push(keyword);
          console.log(
            `✅ Added keyword "${keyword}" to pluginkeywordranks for plugin ${pluginSlug}`
          );
        } else {
          console.log(
            `⚠️ Keyword "${keyword}" already exists for plugin ${pluginSlug}`
          );
        }
      } catch (error) {
        console.error(
          `❌ Error adding keyword "${tag}" for plugin ${pluginSlug}:`,
          error
        );
      }
    }
  }

  console.log(
    `✅ Added ${addedKeywords.length} keywords to pluginkeywordranks for plugin ${pluginSlug}`
  );
  return addedKeywords;
};

// Static method to add keyword for user (ensures user has added the plugin)
pluginKeywordRankSchema.statics.addKeywordForUser = async function (
  userId,
  keywordData
) {
  try {
    const { pluginSlug, pluginName, keyword } = keywordData;

    // Dynamic import to avoid circular dependency
    const { default: AddedPlugin } = await import("./AddedPlugin.js");

    // Check if user has added this plugin
    const addedPlugin = await AddedPlugin.findOne({
      userId,
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!addedPlugin) {
      throw new Error("Plugin not found in user's added plugins");
    }

    // Check if keyword already exists in pluginkeywordranks collection
    const existingKeyword = await this.findOne({
      pluginSlug: pluginSlug.toLowerCase().trim(),
      keyword: keyword.toLowerCase().trim(),
      isActive: true,
    });

    if (existingKeyword) {
      throw new Error(
        `Keyword "${keyword}" already exists for plugin ${pluginSlug}`
      );
    }

    // Add the keyword directly to pluginkeywordranks collection
    console.log(
      `Adding keyword "${keyword}" to pluginkeywordranks for plugin ${pluginSlug}`
    );

    const currentDate = new Date().toLocaleDateString("en-GB"); // Format: dd-mm-yyyy
    const keywordRankRecord = new this({
      pluginSlug: pluginSlug.toLowerCase().trim(),
      pluginName,
      keyword: keyword.toLowerCase().trim(),
      source: "manual",
      rankHistory: {},
      currentRank: null, // Will be populated when rank is fetched
      latestDate: currentDate,
      currentOccurrences: 0, // Will be populated when rank is fetched
      fetchedAt: new Date(),
      isActive: true,
    });

    const savedRecord = await keywordRankRecord.save();
    console.log(
      `✅ Keyword "${keyword}" saved successfully to pluginkeywordranks collection`
    );

    return {
      keyword: keyword.trim(),
      pluginSlug: pluginSlug.toLowerCase(),
      pluginName,
      source: "manual",
      addedAt: new Date(),
    };
  } catch (error) {
    console.error("Error in addKeywordForUser:", error);
    throw error;
  }
};

// Static method to refresh keywords from tags (refetch behavior)
pluginKeywordRankSchema.statics.refreshKeywordsFromTags = async function (
  pluginSlug,
  pluginName,
  tags = {}
) {
  if (!tags || typeof tags !== "object") {
    return null;
  }

  console.log(
    `Refreshing keywords from tags for plugin ${pluginSlug}:`,
    Object.keys(tags)
  );

  const currentDate = new Date().toLocaleDateString("en-GB"); // Format: dd-mm-yyyy
  const updatedKeywords = [];

  // Get existing keywords for this plugin
  const existingKeywords = await this.find({
    pluginSlug: pluginSlug.toLowerCase().trim(),
    isActive: true,
  });

  // Create a set of existing keywords for quick lookup
  const existingKeywordSet = new Set(
    existingKeywords.map((k) => k.keyword.toLowerCase().trim())
  );

  // Add new keywords from tags
  for (const tag of Object.keys(tags)) {
    if (tag && tag.trim()) {
      try {
        const keyword = tag.trim();
        const keywordKey = keyword.toLowerCase().trim();

        // Check if keyword already exists
        if (!existingKeywordSet.has(keywordKey)) {
          // Create new keyword rank record
          const keywordRankRecord = new this({
            pluginSlug: pluginSlug.toLowerCase().trim(),
            pluginName,
            keyword: keywordKey,
            source: "default",
            rankHistory: {},
            currentRank: null, // Will be populated when rank is fetched
            latestDate: currentDate,
            currentOccurrences: 0, // Will be populated when rank is fetched
            fetchedAt: new Date(),
            isActive: true,
          });

          await keywordRankRecord.save();
          updatedKeywords.push(keyword);
          console.log(
            `✅ Added keyword "${keyword}" to pluginkeywordranks for plugin ${pluginSlug}`
          );
        } else {
          console.log(
            `⚠️ Keyword "${keyword}" already exists for plugin ${pluginSlug}`
          );
        }
      } catch (error) {
        console.error(
          `❌ Error adding keyword "${tag}" for plugin ${pluginSlug}:`,
          error
        );
      }
    }
  }

  console.log(
    `✅ Refreshed keywords for plugin ${pluginSlug}: added ${updatedKeywords.length} new keywords`
  );
  return updatedKeywords;
};

// Instance method to get rank history as array
pluginKeywordRankSchema.methods.getRankHistoryArray = function (limit = 30) {
  try {
    if (!this.rankHistory || typeof this.rankHistory !== "object") {
      return [];
    }

    const historyArray = Object.keys(this.rankHistory)
      .map((date) => {
        try {
          const rankData = this.rankHistory[date];
          if (!rankData || typeof rankData !== "object") {
            return null;
          }

          return {
            date: date,
            rank: rankData.rank,
            fetchedAt: rankData.fetchedAt,
          };
        } catch (error) {
          console.error(
            `Error processing rank history entry for date ${date}:`,
            error
          );
          return null;
        }
      })
      .filter((entry) => entry !== null) // Remove null entries
      .sort((a, b) => {
        try {
          // Sort by date descending (newest first)
          const dateA = new Date(a.date.split("-").reverse().join("-"));
          const dateB = new Date(b.date.split("-").reverse().join("-"));
          return dateB - dateA;
        } catch (error) {
          console.error("Error sorting rank history:", error);
          return 0;
        }
      });

    return limit ? historyArray.slice(0, limit) : historyArray;
  } catch (error) {
    console.error("Error in getRankHistoryArray:", error);
    return [];
  }
};

// Instance method to get rank for specific date
pluginKeywordRankSchema.methods.getRankForDate = function (date) {
  if (!this.rankHistory || typeof this.rankHistory !== "object") {
    return null;
  }

  return this.rankHistory[date] ? this.rankHistory[date].rank : null;
};

// Static method to remove keyword for user
pluginKeywordRankSchema.statics.removeKeywordForUser = async function (
  userId,
  keywordId
) {
  try {
    // Dynamic import to avoid circular dependency
    const { default: AddedPlugin } = await import("./AddedPlugin.js");
    const { default: PluginKeyword } = await import("./PluginKeyword.js");

    // Get user's added plugins
    const addedPlugins = await AddedPlugin.find({
      userId,
      isActive: true,
    }).select("pluginSlug");

    if (addedPlugins.length === 0) {
      return null;
    }

    const pluginSlugs = addedPlugins.map((plugin) => plugin.pluginSlug);

    // Parse keywordId to extract pluginSlug and keyword
    // keywordId format: "pluginSlug-keyword"
    const parts = keywordId.split("-");
    if (parts.length < 2) {
      return null;
    }

    const pluginSlug = parts[0];
    const keyword = parts.slice(1).join("-"); // In case keyword contains hyphens

    // Check if this plugin belongs to the user
    if (!pluginSlugs.includes(pluginSlug)) {
      return null;
    }

    // Remove from pluginkeywordranks collection (primary source)
    const deleteResult = await this.deleteOne({
      pluginSlug: pluginSlug.toLowerCase().trim(),
      keyword: keyword.toLowerCase().trim(),
    });

    if (deleteResult.deletedCount === 0) {
      return null;
    }

    // Also remove from pluginkeywords collection if it exists
    try {
      const keywordDoc = await PluginKeyword.findOne({
        pluginSlug: pluginSlug,
        isActive: true,
      });

      if (keywordDoc && keywordDoc.keywords) {
        const keywordKey = keyword.toLowerCase().trim();
        if (keywordDoc.keywords[keywordKey]) {
          delete keywordDoc.keywords[keywordKey];
          keywordDoc.fetchedAt = new Date();
          await keywordDoc.save();
        }
      }
    } catch (error) {
      console.error(
        "Error removing keyword from pluginkeywords collection:",
        error
      );
      // Don't fail the entire operation if this fails
    }

    return { success: true };
  } catch (error) {
    console.error("Error in removeKeywordForUser (PluginKeywordRank):", error);
    throw error;
  }
};

const PluginKeywordRank = mongoose.model(
  "PluginKeywordRank",
  pluginKeywordRankSchema
);

export default PluginKeywordRank;
