import mongoose from "mongoose";

const pluginKeywordSchema = new mongoose.Schema(
  {
    pluginSlug: {
      type: String,
      required: [true, "Plugin slug is required"],
      trim: true,
      lowercase: true,
      unique: true,
    },
    pluginName: {
      type: String,
      required: [true, "Plugin name is required"],
      trim: true,
    },
    // Store all keywords for this plugin as an object
    keywords: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    // Metadata
    fetchedAt: {
      type: Date,
      default: Date.now,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for efficient queries
// pluginSlug index is automatically created by unique: true
pluginKeywordSchema.index({ fetchedAt: -1 });

// Static method to get keywords for a plugin
pluginKeywordSchema.statics.getPluginKeywords = function (pluginSlug) {
  return this.findOne({
    pluginSlug: pluginSlug.toLowerCase(),
    isActive: true,
  });
};

// Static method to get keywords for user's added plugins
pluginKeywordSchema.statics.getUserKeywords = async function (
  userId,
  pluginSlug = null
) {
  try {
    console.log(
      `getUserKeywords called for user ${userId}, plugin: ${
        pluginSlug || "all"
      }`
    );

    // Dynamic import to avoid circular dependency
    const { default: AddedPlugin } = await import("./AddedPlugin.js");
    const { default: PluginKeywordRank } = await import(
      "./PluginKeywordRank.js"
    );

    // Get user's added plugins
    let addedPluginsQuery = { userId, isActive: true };
    if (pluginSlug) {
      addedPluginsQuery.pluginSlug = pluginSlug.toLowerCase();
    }

    console.log("Searching for added plugins with query:", addedPluginsQuery);

    const addedPlugins = await AddedPlugin.find(addedPluginsQuery).select(
      "pluginSlug"
    );

    console.log(`Found ${addedPlugins.length} added plugins for user`);

    if (addedPlugins.length === 0) {
      console.log("No added plugins found for user, returning empty array");
      return [];
    }

    // Get plugin slugs
    const pluginSlugs = addedPlugins.map((plugin) => plugin.pluginSlug);
    console.log("Plugin slugs:", pluginSlugs);

    // Get keywords for these plugins with timeout protection
    let keywordDocs = [];
    try {
      keywordDocs = await this.find({
        pluginSlug: { $in: pluginSlugs },
        isActive: true,
      }); // Removed lean() to ensure fresh data
    } catch (dbError) {
      console.error("Error fetching keyword documents:", dbError);
      return [];
    }

    console.log(`Found ${keywordDocs.length} keyword documents`);

    if (keywordDocs.length === 0) {
      console.log("No keyword documents found, returning empty array");
      return [];
    }

    // Flatten keywords from all plugins
    const allKeywords = [];

    // Collect all keywords first, then batch fetch rank data
    const keywordEntries = [];
    for (const doc of keywordDocs) {
      if (doc.keywords && typeof doc.keywords === "object") {
        console.log(
          `Plugin ${doc.pluginSlug} has ${
            Object.keys(doc.keywords).length
          } keywords:`,
          Object.keys(doc.keywords)
        );
        for (const key of Object.keys(doc.keywords)) {
          const keyword = doc.keywords[key];
          console.log(`Processing keyword "${key}":`, keyword);
          keywordEntries.push({
            doc,
            key,
            keyword,
            keywordText: keyword.keyword || key,
          });
        }
      }
    }

    console.log(`Processing ${keywordEntries.length} keyword entries`);

    // Batch fetch rank records to improve performance
    const rankQueries = keywordEntries.map((entry) => ({
      pluginSlug: entry.doc.pluginSlug.toLowerCase().trim(),
      keyword: entry.keywordText.toLowerCase().trim(),
      isActive: true,
    }));

    let rankRecords = [];
    try {
      if (rankQueries.length > 0) {
        rankRecords = await PluginKeywordRank.find({
          $or: rankQueries,
        }); // Removed lean() to ensure fresh data
      }
    } catch (rankError) {
      console.error("Error batch fetching rank records:", rankError);
      rankRecords = [];
    }

    // Create a map for quick rank record lookup
    const rankRecordMap = new Map();
    rankRecords.forEach((record) => {
      const key = `${record.pluginSlug}-${record.keyword}`;
      rankRecordMap.set(key, record);
    });

    // Process keywords with rank data
    for (const entry of keywordEntries) {
      let latestRank = null;
      let previousRank = null;
      let rankChange = null;
      let lastChecked = null;

      try {
        const rankKey = `${entry.doc.pluginSlug}-${entry.keywordText
          .toLowerCase()
          .trim()}`;
        const rankRecord = rankRecordMap.get(rankKey);

        if (rankRecord && rankRecord.rankHistory) {
          // Process rank history manually since we're using lean()
          const historyEntries = Object.keys(rankRecord.rankHistory)
            .map((date) => ({
              date,
              rank: rankRecord.rankHistory[date].rank,
              fetchedAt: rankRecord.rankHistory[date].fetchedAt,
            }))
            .sort((a, b) => {
              const dateA = new Date(a.date.split("-").reverse().join("-"));
              const dateB = new Date(b.date.split("-").reverse().join("-"));
              return dateB - dateA;
            })
            .slice(0, 2);

          if (historyEntries.length > 0) {
            latestRank = historyEntries[0].rank;
            lastChecked = historyEntries[0].fetchedAt;

            if (historyEntries.length > 1) {
              previousRank = historyEntries[1].rank;
              if (latestRank && previousRank) {
                rankChange = previousRank - latestRank; // Positive means improvement (rank went down in number)
              }
            }
          }
        }
      } catch (rankError) {
        console.error(
          `Error processing rank data for keyword ${entry.keywordText} in plugin ${entry.doc.pluginSlug}:`,
          rankError
        );
      }

      allKeywords.push({
        _id: `${entry.doc.pluginSlug}-${entry.key}`,
        keyword: entry.keywordText,
        pluginSlug: entry.doc.pluginSlug,
        pluginName: entry.doc.pluginName,
        source: entry.keyword.source || "unknown",
        addedAt: entry.keyword.addedAt,
        updatedAt: entry.keyword.updatedAt || entry.keyword.addedAt,
        type: entry.keyword.type || null,
        isActive: entry.keyword.isActive !== false,
        latestRank: latestRank,
        previousRank: previousRank,
        rankChange: rankChange,
        lastChecked: lastChecked,
      });
    }

    console.log(
      `Returning ${allKeywords.length} keywords from getUserKeywords`
    );
    return allKeywords;
  } catch (error) {
    console.error("Error in getUserKeywords:", error);
    console.error("Error stack:", error.stack);
    throw error;
  }
};

// Static method to upsert keywords for a plugin
pluginKeywordSchema.statics.upsertPluginKeywords = async function (
  pluginSlug,
  pluginName,
  keywordsData = {}
) {
  return this.findOneAndUpdate(
    { pluginSlug: pluginSlug.toLowerCase() },
    {
      pluginSlug: pluginSlug.toLowerCase(),
      pluginName,
      keywords: keywordsData,
      fetchedAt: new Date(),
      isActive: true,
    },
    {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
    }
  );
};

// Static method to add keywords from plugin tags with proper default/manual handling
pluginKeywordSchema.statics.addKeywordsFromTags = async function (
  pluginSlug,
  pluginName,
  tags = {}
) {
  if (!tags || typeof tags !== "object") {
    return null;
  }

  // Get existing keywords record
  let existingRecord = await this.findOne({
    pluginSlug: pluginSlug.toLowerCase(),
  });

  let keywordsData = {};

  // If record exists, preserve manual keywords
  if (existingRecord && existingRecord.keywords) {
    // Keep all manual keywords
    Object.keys(existingRecord.keywords).forEach((key) => {
      const keyword = existingRecord.keywords[key];
      if (keyword.source === "manual") {
        keywordsData[key] = keyword;
      }
    });
  }

  // Add new default keywords from tags
  Object.keys(tags).forEach((tag) => {
    if (tag && tag.trim()) {
      const keywordKey = tag.toLowerCase().trim();
      keywordsData[keywordKey] = {
        keyword: tag.trim(),
        addedAt: new Date(),
        source: "default",
        type: "plugin_tags",
      };
    }
  });

  return this.upsertPluginKeywords(pluginSlug, pluginName, keywordsData);
};

// Instance method to get all keywords as array
pluginKeywordSchema.methods.getAllKeywords = function () {
  if (!this.keywords || typeof this.keywords !== "object") {
    return [];
  }

  return Object.keys(this.keywords).map((key) => ({
    keyword: this.keywords[key].keyword || key,
    addedAt: this.keywords[key].addedAt,
    source: this.keywords[key].source || "unknown",
  }));
};

// Instance method to add a keyword
pluginKeywordSchema.methods.addKeyword = function (keyword, source = "manual") {
  if (!this.keywords) {
    this.keywords = {};
  }

  const keywordKey = keyword.toLowerCase().trim();
  this.keywords[keywordKey] = {
    keyword: keyword.trim(),
    addedAt: new Date(),
    source: source,
  };

  this.fetchedAt = new Date();
  return this.save();
};

// Instance method to remove a keyword
pluginKeywordSchema.methods.removeKeyword = function (keyword) {
  if (!this.keywords) {
    return this;
  }

  const keywordKey = keyword.toLowerCase().trim();
  delete this.keywords[keywordKey];

  this.fetchedAt = new Date();
  return this.save();
};

// Static method to refresh keywords from tags (refetch behavior)
pluginKeywordSchema.statics.refreshKeywordsFromTags = async function (
  pluginSlug,
  pluginName,
  tags = {}
) {
  if (!tags || typeof tags !== "object") {
    return null;
  }

  // Get existing keywords record
  let existingRecord = await this.findOne({
    pluginSlug: pluginSlug.toLowerCase(),
  });

  let keywordsData = {};

  // If record exists, preserve manual keywords and update default ones
  if (existingRecord && existingRecord.keywords) {
    // Keep all manual keywords
    Object.keys(existingRecord.keywords).forEach((key) => {
      const keyword = existingRecord.keywords[key];
      if (keyword.source === "manual") {
        keywordsData[key] = keyword;
      }
    });
  }

  // Add/update default keywords from current tags
  Object.keys(tags).forEach((tag) => {
    if (tag && tag.trim()) {
      const keywordKey = tag.toLowerCase().trim();
      keywordsData[keywordKey] = {
        keyword: tag.trim(),
        addedAt: new Date(),
        source: "default",
        type: "plugin_tags",
      };
    }
  });

  return this.upsertPluginKeywords(pluginSlug, pluginName, keywordsData);
};

// Static method to add keyword for user (ensures user has added the plugin)
pluginKeywordSchema.statics.addKeywordForUser = async function (
  userId,
  keywordData
) {
  try {
    const { pluginSlug, pluginName, keyword } = keywordData;

    // Dynamic import to avoid circular dependency
    const { default: AddedPlugin } = await import("./AddedPlugin.js");

    // Check if user has added this plugin
    const addedPlugin = await AddedPlugin.findOne({
      userId,
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!addedPlugin) {
      throw new Error("Plugin not found in user's added plugins");
    }

    // Get or create keyword document for this plugin
    let keywordDoc = await this.findOne({
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!keywordDoc) {
      keywordDoc = new this({
        pluginSlug: pluginSlug.toLowerCase(),
        pluginName,
        keywords: {},
        isActive: true,
      });
    }

    // Check if keyword already exists
    const keywordKey = keyword.toLowerCase().trim();
    if (keywordDoc.keywords && keywordDoc.keywords[keywordKey]) {
      throw new Error(
        `Keyword "${keyword}" already exists for plugin ${pluginSlug}`
      );
    }

    // Add the keyword with the same format as existing keywords
    console.log(`Adding keyword "${keywordKey}" to plugin ${pluginSlug}`);
    keywordDoc.keywords[keywordKey] = {
      keyword: keyword.trim(),
      addedAt: new Date(),
      source: "manual",
      type: "plugin_tags", // Manual keywords should have type "plugin_tags"
    };

    keywordDoc.fetchedAt = new Date();
    console.log(
      `Saving keyword document for plugin ${pluginSlug} with ${
        Object.keys(keywordDoc.keywords).length
      } keywords`
    );
    console.log(`Keywords before save:`, Object.keys(keywordDoc.keywords));

    const savedDoc = await keywordDoc.save();
    console.log(
      `✅ Keyword "${keyword}" saved successfully to pluginkeywords collection`
    );
    console.log(`Keywords after save:`, Object.keys(savedDoc.keywords));

    // Verify the save by fetching the document again
    const verifyDoc = await this.findOne({
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });
    console.log(
      `Verification - Keywords in database:`,
      Object.keys(verifyDoc.keywords)
    );

    return {
      keyword: keyword.trim(),
      pluginSlug: pluginSlug.toLowerCase(),
      pluginName,
      source: "manual",
      addedAt: new Date(),
    };
  } catch (error) {
    console.error("Error in addKeywordForUser:", error);
    throw error;
  }
};

// Static method to remove keyword for user
pluginKeywordSchema.statics.removeKeywordForUser = async function (
  userId,
  keywordId
) {
  try {
    // Dynamic import to avoid circular dependency
    const { default: AddedPlugin } = await import("./AddedPlugin.js");
    const { default: PluginKeywordRank } = await import(
      "./PluginKeywordRank.js"
    );

    // Get user's added plugins
    const addedPlugins = await AddedPlugin.find({
      userId,
      isActive: true,
    }).select("pluginSlug");

    if (addedPlugins.length === 0) {
      return null;
    }

    const pluginSlugs = addedPlugins.map((plugin) => plugin.pluginSlug);

    // Parse keywordId to extract pluginSlug and keyword
    // keywordId format: "pluginSlug-keyword"
    const parts = keywordId.split("-");
    if (parts.length < 2) {
      return null;
    }

    const pluginSlug = parts[0];
    const keyword = parts.slice(1).join("-"); // In case keyword contains hyphens

    // Check if this plugin belongs to the user
    if (!pluginSlugs.includes(pluginSlug)) {
      return null;
    }

    // Find and update keyword document
    const keywordDoc = await this.findOne({
      pluginSlug: pluginSlug,
      isActive: true,
    });

    if (!keywordDoc) {
      return null;
    }

    const keywordKey = keyword.toLowerCase().trim();
    if (!keywordDoc.keywords || !keywordDoc.keywords[keywordKey]) {
      return null;
    }

    // Remove keyword from pluginkeywords collection
    delete keywordDoc.keywords[keywordKey];
    keywordDoc.fetchedAt = new Date();
    await keywordDoc.save();

    // Also remove from pluginkeywordranks collection
    await PluginKeywordRank.deleteOne({
      pluginSlug: pluginSlug.toLowerCase().trim(),
      keyword: keyword.toLowerCase().trim(),
    });

    return { success: true };
  } catch (error) {
    console.error("Error in removeKeywordForUser:", error);
    throw error;
  }
};

// Static method to update keyword rank with history (using pluginkeywordranks collection)
pluginKeywordSchema.statics.updateKeywordRank = async function (
  pluginSlug,
  keyword,
  rank,
  date
) {
  try {
    // Dynamic import to avoid circular dependency
    const { default: PluginKeywordRank } = await import(
      "./PluginKeywordRank.js"
    );

    // Find the keyword document to get plugin name
    const keywordDoc = await this.findOne({
      pluginSlug: pluginSlug.toLowerCase(),
      isActive: true,
    });

    if (!keywordDoc) {
      throw new Error(`Keyword document not found for plugin: ${pluginSlug}`);
    }

    const keywordKey = keyword.toLowerCase().trim();

    // Check if keyword exists, if not, try to wait a bit and check again
    if (!keywordDoc.keywords || !keywordDoc.keywords[keywordKey]) {
      console.log(
        `Keyword "${keyword}" not found in plugin ${pluginSlug}, refreshing document...`
      );

      // Refresh the document from database
      const refreshedDoc = await this.findOne({
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      });

      if (
        !refreshedDoc ||
        !refreshedDoc.keywords ||
        !refreshedDoc.keywords[keywordKey]
      ) {
        throw new Error(
          `Keyword "${keyword}" not found in plugin ${pluginSlug}`
        );
      }

      // Use refreshed document
      keywordDoc.keywords = refreshedDoc.keywords;
      keywordDoc.pluginName = refreshedDoc.pluginName;
    }

    // Get the source from the original keyword
    const keywordData = keywordDoc.keywords[keywordKey];
    const keywordSource = keywordData.source || "default";

    // Use the PluginKeywordRank model to upsert rank data
    const rankRecord = await PluginKeywordRank.upsertKeywordRank(
      pluginSlug,
      keywordDoc.pluginName,
      keyword,
      rank,
      date,
      keywordSource
    );

    return rankRecord;
  } catch (error) {
    console.error("Error in updateKeywordRank:", error);
    throw error;
  }
};

// Static method to get keyword rank history (using pluginkeywordranks collection)
pluginKeywordSchema.statics.getKeywordRankHistory = async function (
  pluginSlug,
  keyword,
  limit = 30
) {
  try {
    // Dynamic import to avoid circular dependency
    const { default: PluginKeywordRank } = await import(
      "./PluginKeywordRank.js"
    );

    // Get rank record from pluginkeywordranks collection
    const rankRecord = await PluginKeywordRank.getKeywordRankRecord(
      pluginSlug,
      keyword
    );

    if (!rankRecord) {
      return [];
    }

    // Return sorted rank history (newest first)
    return rankRecord.getRankHistoryArray(limit);
  } catch (error) {
    console.error("Error in getKeywordRankHistory:", error);
    throw error;
  }
};

const PluginKeyword = mongoose.model("PluginKeyword", pluginKeywordSchema);

export default PluginKeyword;
