import{r as a,a as gs,b as qe}from"./vendor-280e31ee.js";import{N as ss,u as He,a as ps,L as fs,O as ys,b as bs,B as ws,R as js,c as de}from"./router-208768c5.js";import{C as Ns,X as vs,A as ks,I as Ss,M as As,U as Re,S as We,L as Ps,a as ze,b as Cs,T as ve,c as be,B as ts,d as $e,e as as,f as Ds,E as Rs,g as $s,h as Ts,F as Ge,i as Me,H as Qe,D as me,j as Es,k as Ls,P as Ae,R as le,l as rs,m as Te,n as _s,o as Us,p as Fs,q as Oe,r as Ve,s as Bs,t as Is,u as ns,K as Ms,v as Os,w as Be,x as ls,y as Hs,z as zs}from"./icons-9d7a79a3.js";import{R as Pe,B as os,C as Ee,X as Le,Y as _e,T as Ce,a as is,L as Ue,b as Je,c as Ye,P as Ks,d as Vs,e as qs,S as Xe,f as Ws}from"./charts-2d8bc326.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))f(n);new MutationObserver(n=>{for(const c of n)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&f(d)}).observe(document,{childList:!0,subtree:!0});function t(n){const c={};return n.integrity&&(c.integrity=n.integrity),n.referrerPolicy&&(c.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?c.credentials="include":n.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function f(n){if(n.ep)return;n.ep=!0;const c=t(n);fetch(n.href,c)}})();var cs={exports:{}},Ke={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Js=a,Ys=Symbol.for("react.element"),Gs=Symbol.for("react.fragment"),Qs=Object.prototype.hasOwnProperty,Xs=Js.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Zs={key:!0,ref:!0,__self:!0,__source:!0};function ds(s,r,t){var f,n={},c=null,d=null;t!==void 0&&(c=""+t),r.key!==void 0&&(c=""+r.key),r.ref!==void 0&&(d=r.ref);for(f in r)Qs.call(r,f)&&!Zs.hasOwnProperty(f)&&(n[f]=r[f]);if(s&&s.defaultProps)for(f in r=s.defaultProps,r)n[f]===void 0&&(n[f]=r[f]);return{$$typeof:Ys,type:s,key:c,ref:d,props:n,_owner:Xs.current}}Ke.Fragment=Gs;Ke.jsx=ds;Ke.jsxs=ds;cs.exports=Ke;var e=cs.exports,ms,Ze=gs;ms=Ze.createRoot,Ze.hydrateRoot;const us=()=>window.location.origin,Ie=async(s,r={})=>{const t=localStorage.getItem("adminToken"),f=us(),n={headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}}},c={...n,...r,headers:{...n.headers,...r.headers}},d=await fetch(`${f}${s}`,c);if(d.status===401){let w="Authentication failed. Please login again.";try{const $=await d.json();$.message&&$.message.includes("expired")?w="Your session has expired. Please login again.":$.message&&$.message.includes("token")&&(w="Invalid session. Please login again.")}catch{}throw window.dispatchEvent(new CustomEvent("auth-error",{detail:{message:w}})),new Error(w)}return d},xs=a.createContext(),we=()=>{const s=a.useContext(xs);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},et=({children:s})=>{const[r,t]=a.useState(!1),[f,n]=a.useState(null),[c,d]=a.useState(!0),[w,$]=a.useState(null),j=(N="Session expired. Please login again.")=>{console.warn("Auto-logout triggered:",N),window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),t(!1),n(null),window.toast&&window.toast(N,"warning"),window.location.href="/login"};a.useEffect(()=>{const N=localStorage.getItem("adminToken"),h=localStorage.getItem("adminUser"),i=localStorage.getItem("redirectAfterLogin");N&&h&&(t(!0),n(JSON.parse(h))),i&&$(i),d(!1);const b=T=>{const{message:P}=T.detail;j(P)};return window.addEventListener("auth-error",b),()=>{window.removeEventListener("auth-error",b)}},[]);const A={isAuthenticated:r,user:f,login:async(N,h)=>{try{const i=us();console.log(`[AUTH] ${new Date().toISOString()} - Attempting login to:`,`${i}/api/auth/login`),console.log(`[AUTH] Login attempt for email: ${N?N.substring(0,3)+"***":"undefined"}`);const b=new AbortController,T=setTimeout(()=>b.abort(),3e4),P=await fetch(`${i}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:N,password:h}),signal:b.signal});clearTimeout(T),console.log(`[AUTH] Response status: ${P.status}`),console.log("[AUTH] Response headers:",Object.fromEntries(P.headers.entries()));const I=await P.text();console.log("[AUTH] Response text (first 200 chars):",I.substring(0,200));let E;try{E=JSON.parse(I),console.log("[AUTH] Parsed response data:",{success:E.success,message:E.message})}catch(R){console.error("[AUTH] Failed to parse response as JSON:",R),console.error("[AUTH] Raw response text:",I);let C="Server returned invalid response. Please try again.";return I.includes("Internal Server Error")?C="Server is experiencing issues. Please try again in a few moments.":I.includes("timeout")?C="Request timed out. Please check your connection and try again.":I.includes("Database")?C="Database connection issue. Please try again.":P.status>=500?C="Server error occurred. Please try again later.":P.status===404&&(C="Login service not found. Please contact support."),{success:!1,message:C}}if(P.ok&&E.success){console.log("[AUTH] Login successful, storing user data"),localStorage.setItem("adminToken",E.token),localStorage.setItem("adminUser",JSON.stringify(E.user)),t(!0),n(E.user);const R=localStorage.getItem("redirectAfterLogin");return R?(localStorage.removeItem("redirectAfterLogin"),$(null),{success:!0,redirectTo:R}):{success:!0}}else return console.log(`[AUTH] Login failed: ${E.message||"Unknown error"}`),{success:!1,message:E.message||"Login failed. Please check your credentials."}}catch(i){console.error("[AUTH] Login error:",{name:i.name,message:i.message,stack:i.stack});let b="Login failed. Please try again.";return i.name==="AbortError"?b="Request timed out. Please check your connection and try again.":i.message.includes("fetch")?b="Network error. Please check your connection and try again.":i.message.includes("NetworkError")&&(b="Network connection failed. Please try again."),{success:!1,message:b}}},logout:(N=!1)=>{N&&window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),t(!1),n(null)},autoLogout:j,loading:c,redirectPath:w};return e.jsx(xs.Provider,{value:A,children:s})},st=({message:s,type:r="info",duration:t=3e3,onClose:f})=>{const[n,c]=a.useState(!0);a.useEffect(()=>{const j=setTimeout(()=>{c(!1),setTimeout(f,300)},t);return()=>clearTimeout(j)},[t,f]);const d={success:Ns,error:vs,warning:ks,info:Ss},w={success:"bg-green-50 text-green-800 border-green-200",error:"bg-red-50 text-red-800 border-red-200",warning:"bg-yellow-50 text-yellow-800 border-yellow-200",info:"bg-blue-50 text-blue-800 border-blue-200"},$=d[r];return e.jsx("div",{className:`fixed top-4 right-4 z-50 transition-all duration-300 ${n?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"}`,children:e.jsxs("div",{className:`flex items-center p-4 rounded-lg border shadow-lg ${w[r]}`,children:[e.jsx($,{className:"h-5 w-5 mr-3 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s})]})})},tt=({children:s})=>{const[r,t]=a.useState([]),f=(c,d="info",w=3e3)=>{const $=Date.now();t(j=>[...j,{id:$,message:c,type:d,duration:w}])},n=c=>{t(d=>d.filter(w=>w.id!==c))};return qe.useEffect(()=>{window.toast=f},[]),e.jsxs(e.Fragment,{children:[s,r.map(c=>e.jsx(st,{message:c.message,type:c.type,duration:c.duration,onClose:()=>n(c.id)},c.id))]})},at=({children:s})=>{const{isAuthenticated:r,loading:t}=we();return t?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):r?s:e.jsx(ss,{to:"/login",replace:!0})},rt=()=>{const[s,r]=a.useState(!1),t=a.useRef(null),{logout:f,user:n}=we(),c=He();a.useEffect(()=>{const j=y=>{t.current&&!t.current.contains(y.target)&&r(!1)};return document.addEventListener("mousedown",j),()=>{document.removeEventListener("mousedown",j)}},[]);const d=()=>{f(),c("/login"),r(!1)},w=j=>{c(j),r(!1)},$=()=>{const j=[{label:"Profile",icon:Re,onClick:()=>w("/profile")}];return((n==null?void 0:n.role)==="admin"||(n==null?void 0:n.role)==="superadmin")&&j.push({label:"Settings",icon:We,onClick:()=>w("/settings")}),j.push({label:"Logout",icon:Ps,onClick:d,className:"text-red-600 hover:bg-red-50"}),j};return e.jsxs("div",{className:"relative",ref:t,children:[e.jsx("button",{onClick:()=>r(!s),className:"flex items-center p-2 text-gray-600 hover:bg-[#edf1f7] hover:text-gray-800 rounded-md transition-colors",title:"More options",children:e.jsx(As,{className:"h-4 w-4"})}),s&&e.jsx("div",{className:"absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:$().map((j,y)=>{const U=j.icon;return e.jsxs("button",{onClick:j.onClick,className:`w-full flex items-center px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${j.className||"text-gray-700"}`,children:[e.jsx(U,{className:"h-4 w-4 mr-3"}),j.label]},y)})})]})},nt=s=>{let r=0;if(s.length===0)return r.toString();for(let t=0;t<s.length;t++){const f=s.charCodeAt(t);r=(r<<5)-r+f,r=r&r}return Math.abs(r).toString(16)},lt=(s,r=32)=>`https://www.gravatar.com/avatar/${nt(s)}?s=${r}&d=identicon`,ot=()=>{const{user:s}=we(),r=ps(),[t,f]=a.useState(!1),n=()=>{const c=[{name:"Dashboard",path:"/dashboard",icon:Cs},{name:"Plugin Rank",path:"/plugin-rank",icon:ve},{name:"Keyword Analysis",path:"/keyword-analysis",icon:be},{name:"Plugin Data Analysis",path:"/analytics",icon:ts}];return((s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="superadmin")&&c.push({name:"Team Members",path:"/users",icon:$e}),c};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>f(!1)}),e.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${t?"translate-x-0":"-translate-x-full"}`,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[e.jsx("img",{src:"/wpdev.png",className:"",alt:"WPDeveloper Logo"}),e.jsx("button",{onClick:()=>f(!1),className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:e.jsx(ze,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"mt-6",children:n().map(c=>{const d=c.icon,w=r.pathname===c.path;return e.jsxs(fs,{to:c.path,onClick:()=>f(!1),className:`flex items-center px-6 py-3 text-sm font-medium transition-colors ${w?"bg-blue-50 text-blue-600 border-r-2 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[e.jsx(d,{className:"h-5 w-5 mr-3"}),c.name]},c.path)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full overflow-hidden",children:e.jsx("img",{src:(s==null?void 0:s.profileImage)||lt((s==null?void 0:s.email)||""),alt:"Profile",className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:(s==null?void 0:s.name)||"Admin User"}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:(s==null?void 0:s.role)||"member"})]})]}),e.jsx(rt,{})]})})]}),e.jsx("div",{className:"lg:pl-64",children:e.jsx("main",{className:"p-6",children:e.jsx(ys,{})})})]})},it=()=>{const[s,r]=a.useState(""),[t,f]=a.useState(""),[n,c]=a.useState(!1),[d,w]=a.useState(!1),[$,j]=a.useState(""),{login:y}=we(),U=He(),A=async N=>{N.preventDefault(),j(""),w(!0);const h=await y(s,t);if(h.success){const i=h.redirectTo||"/dashboard";U(i)}else j(h.message||"Login failed");w(!1)};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4",children:e.jsx("img",{src:"/wpdev_logo.jpeg",className:"h-16 w-16 rounded-full border"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your admin account"})]}),e.jsxs("form",{onSubmit:A,className:"space-y-6",children:[$&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:$}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(as,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"email",type:"email",value:s,onChange:N=>r(N.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ds,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"password",type:n?"text":"password",value:t,onChange:N=>f(N.target.value),className:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0}),e.jsx("button",{type:"button",onClick:()=>c(!n),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:n?e.jsx(Rs,{className:"h-5 w-5"}):e.jsx($s,{className:"h-5 w-5"})})]})]}),e.jsx("button",{type:"submit",disabled:d,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:d?"Signing in...":"Sign In"})]})]})})})},ye=({isOpen:s,onClose:r,title:t,children:f,maxWidth:n="max-w-xl",fixedHeight:c=!1})=>(a.useEffect(()=>{const d=w=>{w.key==="Escape"&&r()};return s&&(document.addEventListener("keydown",d),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",d),document.body.style.overflow="unset"}},[s,r]),s?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:r}),e.jsxs("div",{className:`relative bg-white rounded-lg shadow-xl ${n} w-full mx-4 transform transition-all ${c?"h-[90vh] flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:t}),e.jsx("button",{onClick:r,className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(ze,{className:"h-5 w-5 text-gray-500"})})]}),e.jsx("div",{className:`p-6 ${c?"flex-1 overflow-y-auto":""}`,children:f})]})]})}):null),ct=({isOpen:s,onClose:r,plugin:t})=>{const f=He(),n=a.useRef(null),[c,d]=a.useState([]),[w,$]=a.useState([]),[j,y]=a.useState(0),[U,A]=a.useState(0),[N,h]=a.useState([]),[i,b]=a.useState({}),[T,P]=a.useState(null),[I,E]=a.useState({}),[R,C]=a.useState(""),[g,p]=a.useState(""),[k,D]=a.useState(!1),[_,L]=a.useState(!0),[O,M]=a.useState(!0),[V,Q]=a.useState(!0),[X,ae]=a.useState(!0),[re,ue]=a.useState(0),ie=(u,F)=>{ue(F)},xe=async()=>{if(t)try{L(!0);const u=localStorage.getItem("adminToken"),q=await fetch(`https://pluginsight.vercel.app/api/analytics/download-data/${t.slug}?days=15`,{headers:{Authorization:`Bearer ${u}`}});if(!q.ok)throw new Error("Failed to fetch download data");const l=await q.json();if(l.success&&l.downloadData){const S=l.downloadData.map(x=>({date:new Date(x.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:x.downloads,fullDate:x.date}));d(S)}else d([])}catch(u){console.error("Error fetching download data:",u),d([])}finally{L(!1)}},he=async()=>{if(t)try{M(!0);const u=localStorage.getItem("adminToken"),q=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-info/${t.slug}`,{headers:{Authorization:`Bearer ${u}`}});if(!q.ok)throw new Error("Failed to fetch plugin information");const l=await q.json();l.success&&l.ratings?($(l.ratings),y(l.totalRatings),A(l.averageRating||0)):($([]),y(0),A(0))}catch(u){console.error("Error fetching ratings data:",u),$([]),y(0),A(0)}finally{M(!1)}},je=async()=>{if(t)try{Q(!0);const u=localStorage.getItem("adminToken"),q=await fetch(`https://pluginsight.vercel.app/api/analytics/rank-history/${t.slug}?days=15`,{headers:{Authorization:`Bearer ${u}`}});if(!q.ok)throw new Error("Failed to fetch rank history");const l=await q.json();if(l.success&&l.rankHistory){const S=l.rankHistory.map(x=>({date:x.date,rank:x.rank,fetchedAt:x.fetchedAt}));h(S)}else h([])}catch(u){console.error("Error fetching rank history:",u),h([])}finally{Q(!1)}},ce=async()=>{if(t)try{ae(!0);const u=localStorage.getItem("adminToken"),q=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-versions/${t.slug}`,{headers:{Authorization:`Bearer ${u}`}});if(!q.ok)throw new Error("Failed to fetch plugin versions");const l=await q.json();l.success?(b(l.versions||{}),P(l.currentVersion),E(l.oldVersions||{})):(b({}),P(null),E({}))}catch(u){console.error("Error fetching versions data:",u),b({}),P(null),E({})}finally{ae(!1)}};a.useEffect(()=>{s&&t&&(xe(),he(),je(),ce())},[s,t]),a.useEffect(()=>{const u=F=>{n.current&&!n.current.contains(F.target)&&(D(!1),p(""))};if(k)return document.addEventListener("mousedown",u),()=>{document.removeEventListener("mousedown",u)}},[k]);const ne=u=>{var F;return u?((F=u.split(/[-–:]|&#8211;/)[0])==null?void 0:F.trim())||u:""},oe=()=>!I||Object.keys(I).length===0?[]:Object.keys(I).filter(u=>u.toLowerCase()==="trunk"||u===T?!1:g.trim()?u.toLowerCase().includes(g.toLowerCase()):!0).sort((u,F)=>{const q=x=>x.split(".").map(v=>parseInt(v)||0),l=q(u),S=q(F);for(let x=0;x<Math.max(l.length,S.length);x++){const v=(S[x]||0)-(l[x]||0);if(v!==0)return v}return 0}),ge=u=>{C(u),D(!1),p("")},m=()=>{D(!k),k||p("")},B=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6"],ee=u=>{const F=Math.PI/180,{cx:q,cy:l,midAngle:S,innerRadius:x,outerRadius:v,startAngle:J,endAngle:Y,fill:Z,payload:ke,percent:Se,value:Ne}=u,H=Math.sin(-F*S),pe=Math.cos(-F*S),o=q+(v+10)*pe,z=l+(v+10)*H,W=q+(v+30)*pe,G=l+(v+30)*H,K=W+(pe>=0?1:-1)*22,se=G,te=pe>=0?"start":"end";return e.jsxs("g",{children:[e.jsxs("text",{x:q,y:l,dy:8,textAnchor:"middle",fill:Z,children:[ke.stars,"★"]}),e.jsx(Xe,{cx:q,cy:l,innerRadius:x,outerRadius:v,startAngle:J,endAngle:Y,fill:Z}),e.jsx(Xe,{cx:q,cy:l,startAngle:J,endAngle:Y,innerRadius:v+6,outerRadius:v+10,fill:Z}),e.jsx("path",{d:`M${o},${z}L${W},${G}L${K},${se}`,stroke:Z,fill:"none"}),e.jsx("circle",{cx:K,cy:se,r:2,fill:Z,stroke:"none"}),e.jsx("text",{x:K+(pe>=0?1:-1)*12,y:se,textAnchor:te,fill:"#333",children:`${Ne} ratings`}),e.jsx("text",{x:K+(pe>=0?1:-1)*12,y:se,dy:18,textAnchor:te,fill:"#999",children:`(${(Se*100).toFixed(1)}%)`})]})};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Ts,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[ne((t==null?void 0:t.displayName)||(t==null?void 0:t.name))," ","Analytics"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download trends and rating analysis"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>f(`/plugin-details/${t==null?void 0:t.slug}`),className:"flex items-center space-x-2 px-3 py-2 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors",title:"View Plugin Details",children:[e.jsx(Ge,{className:"h-4 w-4"}),e.jsx("span",{children:"Plugin Details"})]}),e.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 transition-colors p-2",children:e.jsx(ze,{className:"h-6 w-6"})})]})]}),e.jsxs("div",{className:"flex-1 p-6 space-y-4 overflow-y-auto",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ve,{className:"h-5 w-5 mr-2 text-blue-600"}),"Download Trends (Last 15 Days)"]}),_?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):c.length>0?e.jsx(Pe,{width:"100%",height:300,children:e.jsxs(os,{data:c,children:[e.jsx(Ee,{strokeDasharray:"3 3"}),e.jsx(Le,{dataKey:"date"}),e.jsx(_e,{}),e.jsx(Ce,{formatter:u=>[u.toLocaleString(),"Downloads"],labelFormatter:u=>`Date: ${u}`}),e.jsx(is,{dataKey:"downloads",fill:"#3B82F6",children:e.jsx(Ue,{dataKey:"downloads",position:"top",fontSize:10,formatter:u=>u.toLocaleString()})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No download data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ve,{className:"h-5 w-5 mr-2 text-purple-600"}),"15-Day Rank Change"]}),V?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})}):N.length>0?e.jsx(Pe,{width:"100%",height:300,children:e.jsxs(Je,{data:N,children:[e.jsx(Ee,{strokeDasharray:"3 3"}),e.jsx(Le,{dataKey:"date"}),e.jsx(_e,{domain:["dataMin - 10","dataMax + 10"],reversed:!0,tickFormatter:u=>`#${u}`}),e.jsx(Ce,{formatter:u=>[`#${u}`,"Rank"],labelFormatter:u=>`Date: ${u}`}),e.jsx(Ye,{type:"monotone",dataKey:"rank",stroke:N.length>1&&N[N.length-1].rank<N[0].rank?"#10B981":"#EF4444",strokeWidth:2,dot:{fill:N.length>1&&N[N.length-1].rank<N[0].rank?"#10B981":"#EF4444",strokeWidth:2,r:4},activeDot:{r:6,strokeWidth:2},children:e.jsx(Ue,{dataKey:"rank",position:"top",formatter:u=>`#${u}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rank history data available"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Me,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Rating Distribution"]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-lg font-bold text-gray-900 flex items-center",children:[U?(U/20).toFixed(1):"N/A"," ⭐"]})})]}),O?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"})}):w.length>0?e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 flex-1",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Pe,{width:200,height:200,children:e.jsxs(Ks,{children:[e.jsx(Vs,{activeIndex:re,activeShape:ee,data:[...w].sort((u,F)=>F.stars-u.stars),cx:"50%",cy:"50%",innerRadius:40,outerRadius:60,fill:"#8884d8",dataKey:"value",onMouseEnter:ie,children:w.map((u,F)=>e.jsx(qs,{fill:B[F%B.length]},`cell-${F}`))}),e.jsx(Ce,{formatter:(u,F,q)=>[`${u} ratings`,`${q.payload.stars} Star${q.payload.stars!==1?"s":""}`]})]})})})}),e.jsxs("div",{className:"bg-white rounded-lg p-4 space-y-2 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Breakdown"}),e.jsx("div",{className:"space-y-2",children:[...w].sort((u,F)=>F.stars-u.stars).map((u,F)=>{const q=w.length>0?u.value/w.reduce((l,S)=>l+S.value,0)*100:0;return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex items-center space-x-1 w-12",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700",children:u.stars}),e.jsx(Me,{className:"h-3 w-3 text-yellow-400 fill-current"})]}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500",style:{width:`${q}%`,backgroundColor:B[F%B.length]}})}),e.jsx("div",{className:"text-xs font-medium text-gray-900 w-8 text-right",children:u.value})]},u.stars)})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-2",children:[e.jsx("div",{className:"text-xs font-bold text-gray-500",children:"Total"}),e.jsx("div",{className:"text-xs font-medium text-gray-900",children:j})]})]})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rating data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(Ge,{className:"h-5 w-5 mr-2 text-blue-600"}),"Plugin Downloads"]}),X?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.keys(i).length>0?e.jsxs("div",{className:"space-y-4",children:[T&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(Qe,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:["Version ",T]}),e.jsx("span",{className:"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full",children:"Current"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Latest stable release"})]})]}),e.jsx("a",{href:i[T],target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center transition-colors",title:"Download Current Version",children:e.jsx(me,{className:"h-4 w-4"})})]})}),Object.keys(I).length>0&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(Qe,{className:"h-5 w-5 text-gray-600"})}),e.jsx("span",{className:"font-semibold text-gray-900",children:"Previous Versions"}),e.jsx("span",{className:"bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full",children:"Archive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",ref:n,children:[e.jsxs("button",{type:"button",onClick:m,className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between",children:[e.jsx("span",{className:R?"text-gray-900":"text-gray-500",children:R||"Select a version"}),k?e.jsx(Es,{className:"h-4 w-4 text-gray-400"}):e.jsx(Ls,{className:"h-4 w-4 text-gray-400"})]}),k&&e.jsxs("div",{className:"absolute z-10 w-full bottom-full mb-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:[e.jsx("div",{className:"p-2 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(be,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search versions...",value:g,onChange:u=>p(u.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent",autoFocus:!0})]})}),e.jsx("div",{className:"max-h-48 overflow-y-auto",children:oe().length>0?oe().map(u=>e.jsx("button",{type:"button",onClick:()=>ge(u),className:"w-full px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors",children:u},u)):e.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:g.trim()?"No versions found":"No versions available"})})]})]}),e.jsx("div",{children:e.jsx("a",{href:R?I[R]:"#",target:R?"_blank":"_self",rel:"noopener noreferrer",className:`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ml-3 ${R?"bg-gray-600 hover:bg-gray-700 text-white cursor-pointer":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:R?"Download Selected Version":"Select a version first",onClick:R?void 0:u=>u.preventDefault(),children:e.jsx(me,{className:"h-4 w-4"})})})]})]})})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No version data available"})]})]})]})]})}):null},dt=s=>{if(!s||typeof s!="string")return!1;const r=s.split(".");if(r.length!==3)return!1;try{return r.forEach(t=>{if(t.length===0)throw new Error("Empty JWT part");atob(t.replace(/-/g,"+").replace(/_/g,"/"))}),!0}catch{return!1}},De=()=>{const s=localStorage.getItem("adminToken");return s?dt(s)?s:(console.warn("Invalid JWT token found, clearing localStorage"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),null):null},es=(s,r)=>{var t;((r==null?void 0:r.status)===401||(t=s==null?void 0:s.message)!=null&&t.includes("token"))&&(console.warn("Authentication error detected, clearing tokens"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.pathname!=="/login"&&(window.location.href="/login"))},mt=(s,r,t=!0,f="Auto Refresh")=>{const n=a.useRef(null),c=a.useRef(null),d=a.useCallback(()=>{if(!t||!s)return;const w=new Date,$=new Date(w.getTime()+6*60*60*1e3),j=$.getUTCHours(),y=$.getUTCMinutes(),U=$.getUTCDate(),[A,N]=r.split(":").map(Number),h=j===A&&y===N,i=c.current===U;if(h&&!i){console.log(`🕐 ${f} triggered at ${r} GMT+6`),c.current=U;try{s()}catch(b){console.error(`❌ ${f} failed:`,b)}}},[s,r,t,f]);a.useEffect(()=>{if(!t){n.current&&(clearInterval(n.current),n.current=null);return}return n.current=setInterval(d,6e4),d(),()=>{n.current&&(clearInterval(n.current),n.current=null)}},[d,t]),a.useEffect(()=>()=>{n.current&&clearInterval(n.current)},[])},ut=({plugin:s,onRemove:r,onRefresh:t,canAddPlugins:f})=>{var y,U;const[n,c]=a.useState(!1),[d,w]=a.useState(!1),$=A=>{if(!A)return{formatted:"N/A",daysDiff:"N/A"};try{const N=A.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!N)return{formatted:"N/A",daysDiff:"N/A"};const[,h,i,b]=N,T=`${b}-${i}-${h}`,P=new Date(`${h}-${i}-${b}`),I=new Date;if(isNaN(P.getTime()))return{formatted:"N/A",daysDiff:"N/A"};const E=I-P,R=Math.floor(E/(1e3*60*60*24));return{formatted:T,daysDiff:R}}catch{return{formatted:"N/A",daysDiff:"N/A"}}},j=async()=>{if(t){w(!0);try{await t(s.slug)}finally{w(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 flex-1 overflow-hidden",children:[e.jsxs("div",{className:"w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden",children:[s.icons&&(s.icons["2x"]||s.icons["1x"])?e.jsx("img",{src:s.icons["2x"]||s.icons["1x"],alt:`${s.displayName} icon`,className:"w-full h-full object-cover rounded-lg",onError:A=>{A.target.style.display="none",A.target.nextSibling.style.display="flex"}}):null,e.jsx(rs,{className:`h-6 w-6 text-black ${s.icons&&(s.icons["2x"]||s.icons["1x"])?"hidden":""}`})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-semibold text-gray-900 truncate text-lg",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-500 font-mono whitespace-nowrap",children:s.slug})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:j,disabled:d,className:"text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50",title:"Refresh plugin data",children:e.jsx(le,{className:`h-4 w-4 ${d?"animate-spin":""}`})}),f&&e.jsx("button",{onClick:()=>r(s.slug),className:"text-gray-400 hover:text-red-500 transition-colors p-1",title:"Remove plugin",children:e.jsx(Te,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-x-4",children:[e.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800",children:["v",s.version||"N/A"]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.currentRank||"N/A"]}),((y=s.rankHistory)==null?void 0:y.rankChange)!==null&&((U=s.rankHistory)==null?void 0:U.rankChange)!==void 0&&e.jsxs("span",{className:`text-xs ${s.rankHistory.rankChange>0?"text-green-600":s.rankHistory.rankChange<0?"text-red-600":"text-gray-600"}`,children:[s.rankHistory.rankChange>0?"↑":s.rankHistory.rankChange<0?"↓":"→",Math.abs(s.rankHistory.rankChange)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Released"}),e.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded ${(()=>{const A=s.lastReleaseDate||s.lastFetched,N=$(A);return N.daysDiff==="N/A"||N.daysDiff<=20?"bg-gray-100 text-gray-700":"bg-yellow-50 text-yellow-700"})()}`,children:(()=>{const A=s.lastReleaseDate||s.lastFetched,N=$(A);return e.jsxs(e.Fragment,{children:[N.formatted,N.daysDiff!=="N/A"&&e.jsxs("span",{className:"text-xs ml-1",children:["(",N.daysDiff," days)"]})]})})()})]})]}),e.jsxs("div",{className:"mt-4 border border-gray-200 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 px-3 py-2 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Download Trends"}),s.downloadTrend&&e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${s.downloadTrend.isPositive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[s.downloadTrend.isPositive?"↑":"↓"," ",s.downloadTrend.changePercent,"%"]})]})}),s.downloadTrend?e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Yesterday"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right",children:s.downloadTrend.yesterdayDownloads.toLocaleString()})]}),e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Day Before"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right",children:s.downloadTrend.dayBeforeDownloads.toLocaleString()})]})]}),e.jsx("tfoot",{className:"bg-gray-100 border-t border-gray-200",children:e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 text-sm font-semibold text-gray-900",children:"Changes"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:e.jsxs("span",{className:`text-sm font-bold ${s.downloadTrend.change>=0?"text-green-600":"text-red-600"}`,children:[s.downloadTrend.change>=0?"+":"",s.downloadTrend.change.toLocaleString()]})})]})})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("span",{className:"text-xs text-gray-500",children:"No download trend data available"})})]}),e.jsx("div",{className:"pt-4 border-t border-gray-100",children:e.jsxs("button",{onClick:()=>c(!0),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2",children:[e.jsx(_s,{className:"h-4 w-4"}),e.jsx("span",{children:"View Chart & Analytics"})]})})]}),e.jsx(ct,{isOpen:n,onClose:()=>c(!1),plugin:s})]})},xt=()=>{var ee,u,F,q;const{user:s,autoLogout:r}=we(),[t,f]=a.useState(!1),[n,c]=a.useState(""),[d,w]=a.useState(!1),[$,j]=a.useState(!1),[y,U]=a.useState(null),[A,N]=a.useState([]),[h,i]=a.useState(null),[b,T]=a.useState(!1),[P,I]=a.useState(!1),[E,R]=a.useState(null),[C,g]=a.useState(!1),[p,k]=a.useState(!1),[D,_]=a.useState(null),[L,O]=a.useState(""),[M,V]=a.useState(!1),Q=s&&["admin","superadmin"].includes(s.role),X=async()=>{try{const l=De();if(!l){console.warn("No valid authentication token found for database check"),r("No valid authentication token found. Please login again.");return}const x=await fetch("https://pluginsight.vercel.app/api/plugins/check-database",{headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}});if(x.ok){const v=await x.json();v.success&&j(v.hasPlugins)}else if(x.status===401){console.warn("Authentication failed during database check");let v="Your session has expired. Please login again.";try{const J=await x.json();J.message&&(v=J.message)}catch{}r(v)}else es(null,x)}catch(l){if(console.error("Error checking database status:",l),l.message&&(l.message.includes("expired")||l.message.includes("token")||l.message.includes("Authentication failed"))){console.warn("JWT token error detected during database check:",l.message),r(l.message);return}es(l)}},ae=async()=>{try{w(!0),U({current:0,total:0,page:0,totalPages:0,successCount:0,errorCount:0,percentComplete:0,estimatedTimeRemaining:null,averageTimePerPage:null,pluginsPerSecond:0,message:"Starting full plugin fetch (all 55,540+ plugins)..."});const l=De();if(!l){console.error("No valid authentication token found"),r("No valid authentication token found. Please login again.");return}const x=await fetch("https://pluginsight.vercel.app/api/plugins/fetch-all",{method:"POST",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}});if(!x.ok){if(x.status===401){console.warn("Authentication failed during plugin fetch");let Y="Your session has expired. Please login again.";try{const Z=await x.json();Z.message&&(Y=Z.message)}catch{}r(Y);return}throw new Error(`HTTP error! status: ${x.status}`)}const v=x.body.getReader(),J=new TextDecoder;for(;;){const{done:Y,value:Z}=await v.read();if(Y)break;const Se=J.decode(Z).split(`
`).filter(Ne=>Ne.trim());for(const Ne of Se)try{const H=JSON.parse(Ne);if(H.type==="progress")U({current:H.current||0,total:H.total||0,page:H.page||0,totalPages:H.totalPages||0,successCount:H.successCount||0,errorCount:H.errorCount||0,percentComplete:H.percentComplete||0,estimatedTimeRemaining:H.estimatedTimeRemaining,averageTimePerPage:H.averageTimePerPage,pluginsPerSecond:H.pluginsPerSecond||0,message:H.message||"Processing..."});else if(H.type==="complete")U({current:H.summary.totalProcessedPlugins,total:H.summary.totalPlugins,page:H.summary.totalPages,totalPages:H.summary.totalPages,successCount:H.summary.successfulPages,errorCount:H.summary.failedPages,percentComplete:100,averageTimePerPage:H.summary.averageTimePerPage,pluginsPerSecond:H.summary.averagePluginsPerSecond,successRate:H.summary.successRate,totalDuration:H.summary.totalDuration,message:`✅ Fetch completed! ${H.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(H.summary.totalDuration/1e3/60)} minutes`}),H.errors&&H.errors.length>0&&console.warn("Some errors occurred during fetch:",H.errors),window.toast(`Successfully fetched ${H.summary.totalProcessedPlugins.toLocaleString()} plugins!`,"success");else if(H.type==="error")throw new Error(H.message||"Fetch failed")}catch(H){console.warn("Failed to parse streaming data:",H)}}}catch(l){if(console.error("Fetch all plugins error:",l),l.message&&(l.message.includes("expired")||l.message.includes("token")||l.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin fetch:",l.message),r(l.message);return}U({current:0,total:0,message:`❌ Full fetch failed: ${l.message}`,error:!0}),window.toast(`Fetch failed: ${l.message}`,"error")}finally{w(!1),setTimeout(()=>U(null),1e4),X()}},re=async()=>{try{const l=await Ie("/api/plugins/added");if(!l.ok){let x=`HTTP error! status: ${l.status}`;try{const v=await l.json();v.message&&(x=v.message)}catch{}throw new Error(x)}const S=await l.json();if(S.success){const x=S.addedPlugins.map(v=>{var J;return{slug:v.pluginSlug,name:v.pluginName,displayName:v.displayName,currentRank:v.currentRank,rankGrowth:((J=v.rankHistory)==null?void 0:J.rankChange)||0,lastFetched:v.lastUpdated,short_description:v.short_description,version:v.version,lastReleaseDate:v.lastReleaseDate,icons:v.icons||{},rating:v.rating,numRatings:v.numRatings||0,currentVersion:v.currentVersion,previousVersions:v.previousVersions||[],rankHistory:v.rankHistory,downloadTrend:v.downloadTrend,downloadDataHistory:v.downloadDataHistory||[],reviewStats:v.reviewStats,versionInfo:v.versionInfo,pluginInformation:v.pluginInformation}});N(x)}else console.warn("Failed to load added plugins:",S.message)}catch(l){console.error("Error loading added plugins:",l),l.name==="TypeError"&&l.message.includes("Failed to fetch")?(console.warn("Unable to connect to server - backend may be down"),N([])):(console.warn("Failed to load added plugins:",l.message),N([]))}},ue=l=>{try{localStorage.setItem("pluginData",JSON.stringify(l))}catch(S){console.error("Error storing plugin data in localStorage:",S)}},ie=()=>{try{const l=localStorage.getItem("pluginData");return l?JSON.parse(l):null}catch(l){return console.error("Error retrieving plugin data from localStorage:",l),null}},xe=()=>{localStorage.removeItem("pluginData")},he=async()=>{if(!n.trim()){window.toast("Please enter a plugin slug","warning");return}try{T(!0);const l=`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${n.trim()}&request[fields][icons]=true`,S=await fetch(l);if(!S.ok)throw new Error(`WordPress API error: ${S.status}`);const x=await S.json();if(x.error){window.toast(`Plugin not found: ${x.error}`,"error"),i(null);return}const v={slug:x.slug,name:x.name,version:x.version,author:x.author,rating:x.rating,active_installs:x.active_installs,num_ratings:x.num_ratings,downloaded:x.downloaded,last_updated:x.last_updated,short_description:x.short_description,homepage:x.homepage,requires:x.requires,tested:x.tested,requires_php:x.requires_php,icons:x.icons?{"1x":x.icons["1x"],"2x":x.icons["2x"]}:{},tags:x.tags?Object.keys(x.tags).slice(0,10).reduce((J,Y)=>(J[Y]=x.tags[Y],J),{}):{}};ue(v),i({slug:x.slug,name:x.name,version:x.version,author:x.author,rating:x.rating,active_installs:x.active_installs,num_ratings:x.num_ratings,downloaded:x.downloaded,last_updated:x.last_updated,homepage:x.homepage,requires:x.requires,tested:x.tested,requires_php:x.requires_php}),window.toast("Plugin data fetched successfully from WordPress API","success")}catch(l){console.error("Error fetching plugin data:",l),window.toast("Failed to fetch plugin data from WordPress API","error"),i(null)}finally{T(!1)}};a.useEffect(()=>{re(),X()},[]);const je=async()=>{const l=ie();if(!l){window.toast("Please fetch plugin data first by clicking the Fetch button","warning");return}if(!n.trim()){window.toast("Please enter a plugin slug","warning");return}g(!0);try{const S=De();if(!S){console.error("No valid authentication token found"),r("No valid authentication token found. Please login again.");return}const v=await fetch("https://pluginsight.vercel.app/api/plugins/added-with-data",{method:"POST",headers:{Authorization:`Bearer ${S}`,"Content-Type":"application/json"},body:JSON.stringify({slug:n.trim(),pluginData:l})});if(!v.ok){if(v.status===401){console.warn("Authentication failed during plugin addition");let Y="Your session has expired. Please login again.";try{const Z=await v.json();Z.message&&(Y=Z.message)}catch{}r(Y);return}else if(v.status===413){console.warn("Payload too large error during plugin addition");try{const Y=await v.json();window.toast(Y.message||"Plugin data is too large. Please try again or contact support.","error")}catch{window.toast("Plugin data is too large. Please try again or contact support.","error")}return}}const J=await v.json();J.success?(window.toast(J.message,"success"),xe(),c(""),f(!1),i(null),await re()):window.toast(J.message||"Failed to add plugin","error")}catch(S){if(console.error("Add plugin error:",S),S.message&&(S.message.includes("expired")||S.message.includes("token")||S.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin addition:",S.message),r(S.message);return}window.toast("Failed to add plugin. Please try again.","error")}finally{g(!1)}},ce=l=>{const S=A.find(x=>x.slug===l)||addedPluginsListData.find(x=>x.slug===l);R(S),I(!0)},ne=async()=>{if(E)try{const l=De();if(!l){console.error("No valid authentication token found"),r("No valid authentication token found. Please login again.");return}const x=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${E.slug}`,{method:"DELETE",headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"}});if(!x.ok&&x.status===401){console.warn("Authentication failed during plugin removal");let J="Your session has expired. Please login again.";try{const Y=await x.json();Y.message&&(J=Y.message)}catch{}r(J);return}const v=await x.json();v.success?(window.toast("Plugin removed successfully","success"),await re()):window.toast(v.message||"Failed to remove plugin","error")}catch(l){if(console.error("Remove plugin error:",l),l.message&&(l.message.includes("expired")||l.message.includes("token")||l.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin removal:",l.message),r(l.message);return}window.toast("Failed to remove plugin","error")}finally{I(!1),R(null)}},oe=async l=>{try{const S=De();if(!S){console.error("No valid authentication token found"),r("No valid authentication token found. Please login again.");return}const v=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${l}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${S}`,"Content-Type":"application/json"}});if(!v.ok&&v.status===401){console.warn("Authentication failed during plugin refresh");let Y="Your session has expired. Please login again.";try{const Z=await v.json();Z.message&&(Y=Z.message)}catch{}r(Y);return}const J=await v.json();J.success?(window.toast(J.message,"success"),await re()):window.toast(J.message||"Failed to refresh plugin","error")}catch(S){if(console.error("Refresh plugin error:",S),S.message&&(S.message.includes("expired")||S.message.includes("token")||S.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin refresh:",S.message),r(S.message);return}window.toast("Failed to refresh plugin","error")}},ge=async()=>{try{k(!0),_({current:0,total:0,currentPlugin:null});const l=await Ie("/api/plugins/added");if(!l.ok)throw new Error(`Failed to fetch plugins: ${l.status}`);const S=await l.json();if(!S.success||!S.addedPlugins||S.addedPlugins.length===0){window.toast("No plugins found to refresh","warning");return}const x=S.addedPlugins.map(v=>v.slug);_({current:0,total:x.length,currentPlugin:null}),window.toast(`Starting refresh of ${x.length} plugins...`,"info");for(const v of x){const J=x.indexOf(v)+1;_({current:J,total:x.length,currentPlugin:v});try{const Z=await(await Ie(`/api/plugins/added/${v}/refresh`,{method:"POST"})).json();Z.success?console.log(`Successfully refreshed plugin: ${v}`):console.warn(`Failed to refresh plugin ${v}: ${Z.message}`)}catch(Y){console.error(`Error refreshing plugin ${v}:`,Y)}}window.toast("All plugins refreshed successfully!","success"),await re()}catch(l){if(console.error("Refresh all plugins error:",l),l.message&&(l.message.includes("expired")||l.message.includes("token")||l.message.includes("Authentication failed"))){console.warn("JWT token error detected during refresh all:",l.message),r(l.message);return}window.toast("Failed to refresh all plugins","error")}finally{k(!1),_(null)}};mt(async()=>{try{V(!0),console.log("🕐 Auto-refresh triggered at 9:00 AM GMT+6"),window.toast("Auto-refreshing all plugins...","info"),await ge()}catch(l){console.error("Auto-refresh failed:",l),window.toast("Auto-refresh failed","error")}finally{V(!1)}},"09:00",!0,"Dashboard Plugin Refresh");const B=A.filter(l=>{var x,v;if(!L.trim())return!0;const S=L.toLowerCase();return((x=l.name)==null?void 0:x.toLowerCase().includes(S))||((v=l.slug)==null?void 0:v.toLowerCase().includes(S))}).sort((l,S)=>{const x=l.name||l.slug||"",v=S.name||S.slug||"";return x.toLowerCase().localeCompare(v.toLowerCase())});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-900",children:"Welcome to Admin Dashboard"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your plugins, analyze keywords, and track performance all in one place."})]}),e.jsxs("div",{className:"flex gap-4",children:[Q&&e.jsx("button",{onClick:ae,disabled:d,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${d?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white`,title:"Fetch all 55,540+ plugins from WordPress repository",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Fetching All..."]}):e.jsxs(e.Fragment,{children:[e.jsx(me,{className:"h-4 w-4 mr-2"}),$?"Refetch":"Fetch"]})}),Q&&e.jsxs("button",{onClick:()=>f(!0),disabled:d,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${d?"bg-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700"} text-white`,children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Add Plugin"]})]})]}),y&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg border ${y.error?"bg-red-50 border-red-200":"bg-blue-50 border-blue-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:`text-sm font-medium ${y.error?"text-red-900":"text-blue-900"}`,children:y.message}),y.total>0&&e.jsxs("span",{className:`text-sm ${y.error?"text-red-700":"text-blue-700"}`,children:[(ee=y.current)==null?void 0:ee.toLocaleString(),"/",(u=y.total)==null?void 0:u.toLocaleString()]})]}),y.page&&y.totalPages&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["Page: ",y.page,"/",y.totalPages]}),e.jsxs("div",{children:["Progress: ",y.percentComplete||0,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["✅ ",(F=y.successCount)==null?void 0:F.toLocaleString()," success"]}),y.errorCount>0&&e.jsxs("div",{className:"text-red-600",children:["❌ ",(q=y.errorCount)==null?void 0:q.toLocaleString()," errors"]})]})]}),(y.pluginsPerSecond>0||y.estimatedTimeRemaining)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600",children:[y.pluginsPerSecond>0&&e.jsxs("div",{children:["Speed: ",y.pluginsPerSecond," plugins/sec"]}),y.estimatedTimeRemaining&&e.jsxs("div",{children:["ETA:"," ",Math.round(y.estimatedTimeRemaining/1e3/60)," ","min"]}),y.averageTimePerPage&&e.jsxs("div",{children:["Avg: ",Math.round(y.averageTimePerPage/1e3),"s/page"]}),y.successRate&&e.jsxs("div",{children:["Success Rate: ",y.successRate,"%"]})]}),y.totalDuration&&e.jsx("div",{className:"mb-3 text-xs text-green-700 bg-green-50 p-2 rounded",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:["Duration:"," ",Math.round(y.totalDuration/1e3/60)," ","minutes"]}),e.jsxs("div",{children:["Avg Speed: ",y.pluginsPerSecond," plugins/sec"]}),e.jsxs("div",{children:["Success Rate: ",y.successRate,"%"]}),e.jsxs("div",{children:["Pages: ",y.successCount,"/",y.totalPages]})]})}),y.total>0&&!y.error&&e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-3",children:e.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center",style:{width:`${Math.max(2,y.percentComplete||y.current/y.total*100)}%`},children:e.jsxs("span",{className:"text-xs text-white font-medium",children:[y.percentComplete||Math.round(y.current/y.total*100),"%"]})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Added Plugins"}),e.jsx("div",{className:"text-sm text-gray-500",children:A.length>0?`Showing ${B.length} of ${A.length} plugins`:"No plugins added yet"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[A.length>0&&e.jsxs("div",{className:"relative",children:[e.jsx(be,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search plugins...",value:L,onChange:l=>O(l.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"})]}),Q&&A.length>0&&e.jsx("button",{onClick:ge,disabled:p||M,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${p||M?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white text-sm`,title:M?"Auto-refresh in progress...":"Refresh all plugins sequentially",children:p||M?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),M?"Auto-refreshing...":"Refreshing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(le,{className:"h-4 w-4 mr-2"}),"Refresh All"]})})]})]}),D&&e.jsxs("div",{className:"mb-6 p-4 rounded-lg border bg-blue-50 border-blue-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Refreshing plugins..."}),e.jsxs("span",{className:"text-sm text-blue-700",children:[D.current,"/",D.total]})]}),D.currentPlugin&&e.jsxs("div",{className:"text-sm text-blue-700 mb-2",children:["Currently refreshing:"," ",e.jsx("span",{className:"font-mono",children:D.currentPlugin})]}),e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.max(2,D.current/D.total*100)}%`}})}),e.jsx("div",{className:"text-xs text-blue-600 mt-2",children:"Note: Each plugin refresh has a 60-second delay to avoid overwhelming the backend."})]}),A.length>0?B.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:B.map(l=>e.jsx(ut,{plugin:l,onRemove:ce,onRefresh:oe,canAddPlugins:Q},l.slug))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(be,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins found"}),e.jsxs("p",{className:"text-gray-600",children:['No plugins match your search query "',L,'". Try adjusting your search terms.']})]}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(rs,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins added yet"}),e.jsx("p",{className:"text-gray-600",children:'Start tracking your WordPress plugins by adding them to your dashboard using the "Add Plugin" button above.'})]})]}),e.jsx(ye,{isOpen:t,onClose:()=>{C||(f(!1),i(null),c(""),xe())},title:"Add New Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pluginSlug",className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"pluginSlug",type:"text",value:n,onChange:l=>c(l.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., my-awesome-plugin"}),e.jsx("button",{onClick:he,disabled:b||!n.trim(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Fetching..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(me,{className:"h-4 w-4"}),e.jsx("span",{children:"Fetch"})]})})]})]}),h&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-green-900",children:"Plugin Information (Fetched from WordPress API)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Version:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.version||"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Author:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:(()=>{const l=h.author;if(!l)return"N/A";const S=l.match(/<a[^>]*>(.*?)<\/a>/);return S?S[1]:l})()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Rating:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.rating?`${h.rating}/100`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Active Installs:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.active_installs?h.active_installs.toLocaleString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Last Updated:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.last_updated||"N/A"})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"text-green-700",children:"WordPress Requirements:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-900",children:["WP ",h.requires||"N/A"," | Tested up to"," ",h.tested||"N/A"," | PHP"," ",h.requires_php||"N/A"]})]})]})]}),!h&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Step 1:"}),' Enter a plugin slug and click "Fetch" to retrieve plugin information from WordPress API.',e.jsx("br",{}),e.jsx("strong",{children:"Step 2:"}),' Once plugin data is displayed, click "Add Plugin" to add it to your dashboard.']})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{f(!1),i(null),c(""),xe()},disabled:C,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),e.jsx("button",{onClick:je,disabled:C||!h,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:C?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Adding..."})]}):e.jsx("span",{children:"Add Plugin"})})]})]})}),e.jsx(ye,{isOpen:P,onClose:()=>{I(!1),R(null)},title:"Confirm Delete",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Te,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Delete Plugin"}),e.jsxs("p",{className:"text-gray-600",children:['Are you sure you want to remove "',(E==null?void 0:E.displayName)||(E==null?void 0:E.name),'" from your added plugins?']})]})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:[e.jsx("strong",{children:"Warning:"})," This action cannot be undone. The plugin will be removed from your dashboard and you'll need to add it again if you want to track it."]})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{I(!1),R(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsxs("button",{onClick:ne,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[e.jsx(Te,{className:"h-4 w-4"}),e.jsx("span",{children:"Delete Plugin"})]})]})]})})]})},ht=()=>{var E,R,C;const[s,r]=a.useState([]),[t,f]=a.useState(""),[n,c]=a.useState("7"),[d,w]=a.useState({start:"",end:""}),[$,j]=a.useState([]),[y,U]=a.useState(!1),[A,N]=a.useState(!1);console.log("Chart data: ",$);const h=g=>{var V;const{cx:p,cy:k,payload:D}=g;if(!t||!D)return null;const _=((V=s.find(Q=>Q.pluginSlug===t))==null?void 0:V.displayName)||t,L=D[`${_}_trend`],O=D[_];if(O==null)return null;let M="#3B82F6";return L==="improvement"?M="#10B981":L==="decline"&&(M="#EF4444"),e.jsx("circle",{cx:p,cy:k,r:5,fill:M,stroke:M,strokeWidth:2})},i=async()=>{var g;try{const p=localStorage.getItem("adminToken"),D=await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${p}`,"Content-Type":"application/json"}});if(!D.ok)throw new Error(`HTTP error! status: ${D.status}`);const _=await D.json();_.success?(r(_.plugins),console.log(`✅ Loaded ${((g=_.plugins)==null?void 0:g.length)||0} plugins from plugininformations collection (${_.pluginsWithRankHistory||0} with rank history)`)):console.warn("Failed to load plugins:",_.message)}catch(p){console.error("Error loading plugins from plugininformations collection:",p),p.name==="TypeError"&&p.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins from database","error")}},b=async()=>{if(!t){j([]);return}try{U(!0);const g=s.find(k=>k.pluginSlug===t);if(!g){console.error("Selected plugin not found in loaded plugins"),window.toast("Selected plugin not found","error"),j([]);return}if(!g.rankHistory||!Array.isArray(g.rankHistory)){console.log(`Plugin ${t} has no rank history data`),j([]);return}console.log(`📊 Processing rank history for ${t}: ${g.rankHistory.length} entries`);const p=T(g);j(p)}catch(g){console.error("Error loading chart data:",g),window.toast("Failed to load chart data","error")}finally{U(!1)}},T=g=>{if(!g.rankHistory||!Array.isArray(g.rankHistory))return[];const p=g.displayName||g.pluginName||g.pluginSlug;let k=g.rankHistory;if(n!=="custom"){const L=parseInt(n),O=new Date;O.setDate(O.getDate()-L),k=g.rankHistory.filter(M=>{const[V,Q,X]=M.date.split("-");return new Date(X,Q-1,V)>=O})}else if(d.start&&d.end){const L=new Date(d.start),O=new Date(d.end);k=g.rankHistory.filter(M=>{const[V,Q,X]=M.date.split("-"),ae=new Date(X,Q-1,V);return ae>=L&&ae<=O})}return k.sort((L,O)=>{const[M,V,Q]=L.date.split("-"),[X,ae,re]=O.date.split("-"),ue=new Date(Q,V-1,M),ie=new Date(re,ae-1,X);return ue-ie}).map((L,O,M)=>{const V=M[O-1];let Q="stable";return V&&(L.previousRank<V.previousRank?Q="improvement":L.previousRank>V.previousRank&&(Q="decline")),{date:L.date,[p]:L.previousRank,[`${p}_trend`]:Q}})},P=async()=>{if(!t){window.toast("Please select a plugin first","warning");return}try{U(!0),window.toast("Refreshing chart data...","info"),await b(),window.toast("Chart data refreshed successfully","success")}catch(g){console.error("Error refreshing chart data:",g),window.toast("Failed to refresh chart data","error")}finally{U(!1)}};a.useEffect(()=>{i()},[]),a.useEffect(()=>{b()},[t,n,d]);const I=g=>{c(g),N(g==="custom")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(ve,{className:"h-8 w-8 text-blue-600 mr-3"}),"Plugin Rank Analysis"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track ranking trends for your added plugins"})]})})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plugin"}),e.jsxs("select",{value:t,onChange:g=>f(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(g=>e.jsx("option",{value:g.pluginSlug,children:g.displayName},g.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:n,onChange:g=>I(g.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom range"})]})]}),A&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:d.start,onChange:g=>w(p=>({...p,start:g.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:d.end,onChange:g=>w(p=>({...p,end:g.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:P,disabled:y||!t,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Fetch latest rank from WordPress and save to database",children:[e.jsx(le,{className:`h-4 w-4 mr-2 ${y?"animate-spin":""}`}),"Refresh"]})})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:["Plugin Rank Trends",t&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",((E=s.find(g=>g.pluginSlug===t))==null?void 0:E.displayName)||t,")"]})]}),y?e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading chart data..."})]})}):$.length>0?e.jsx("div",{className:"h-96",children:e.jsx(Pe,{width:"100%",height:"100%",children:e.jsxs(Je,{data:$,children:[e.jsx(Ee,{strokeDasharray:"3 3"}),e.jsx(Le,{dataKey:"date",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(_e,{tick:{fontSize:12},domain:(()=>{var Q;if($.length===0)return[1,100];const g=((Q=s.find(X=>X.pluginSlug===t))==null?void 0:Q.displayName)||t,p=$.map(X=>X[g]).filter(X=>X!=null);if(p.length===0)return[1,100];const k=Math.min(...p),D=Math.max(...p),_=p[p.length-1],L=Math.max(1,_-10),O=_+10,M=Math.min(L,k-2),V=Math.max(O,D+2);return[M,V]})(),reversed:!0,label:{value:"Rank",angle:-90,position:"insideLeft"},allowDecimals:!1,type:"number"}),e.jsx(Ce,{labelFormatter:g=>`Date: ${g}`,formatter:(g,p)=>[g?`#${g}`:"No data",p]}),e.jsx(Ws,{}),t&&e.jsx(Ye,{type:"monotone",dataKey:((R=s.find(g=>g.pluginSlug===t))==null?void 0:R.displayName)||t,stroke:"#3B82F6",strokeWidth:2,dot:e.jsx(h,{}),connectNulls:!1,activeDot:{r:6,stroke:"#3B82F6",strokeWidth:2},children:e.jsx(Ue,{dataKey:((C=s.find(g=>g.pluginSlug===t))==null?void 0:C.displayName)||t,position:"top",fontSize:10,fill:"#374151",formatter:g=>g?`#${g}`:""})},t)]})})}):e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Data Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:t?"No rank history found for the selected plugin and date range":"Select a plugin to view its rank trends"}),s.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Add plugins from the Dashboard first to see their ranking trends."})]})})]})]})},hs=s=>{const r=document.createElement("textarea");return r.innerHTML=s,r.value},gt=(s,r=40)=>{const t=hs(s);if(t.length<=r)return t;const f=t.split(" ");let n=f[0];for(let c=1;c<f.length&&(n+" "+f[c]).length<=r-3;c++)n+=" "+f[c];return n+"..."},pt=()=>{var pe;const[s,r]=a.useState("performance"),[t,f]=a.useState([]),[n,c]=a.useState(""),[d,w]=a.useState([]),[$,j]=a.useState(!1),[y,U]=a.useState(!1),[A,N]=a.useState(""),[h,i]=a.useState(""),[b,T]=a.useState(!1),[P,I]=a.useState(new Set),[E,R]=a.useState(!1),[C,g]=a.useState(new Set),[p,k]=a.useState(null),[D,_]=a.useState(!1),[L,O]=a.useState([]),[M,V]=a.useState(!1),[Q,X]=a.useState(!1),[ae,re]=a.useState(""),[ue,ie]=a.useState({}),[xe,he]=a.useState(!1),[je,ce]=a.useState(!1),[ne,oe]=a.useState(null),[ge,m]=a.useState([]),[B,ee]=a.useState([]),[u,F]=a.useState(!1),[q,l]=a.useState(!1),S=async()=>{var o;try{const z=localStorage.getItem("adminToken"),K=await(await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();K.success?(f(K.plugins),console.log(`✅ Loaded ${((o=K.plugins)==null?void 0:o.length)||0} plugins from plugininformations collection for keyword analysis`)):console.error("Failed to load plugins:",K.message)}catch(z){console.error("Error loading plugins from plugininformations collection:",z),window.toast("Failed to load plugins from database","error")}},x=async()=>{try{if(j(!0),!n){w([]),ie({}),j(!1);return}const o=localStorage.getItem("adminToken"),W=`https://pluginsight.vercel.app/api/keywords?pluginSlug=${n}`,K=await(await fetch(W,{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();if(K.success){const se=K.keywords.map(fe=>({...fe,position:fe.latestRank,lastChecked:fe.lastChecked||fe.updatedAt}));w(se);const te={};se.forEach(fe=>{te[fe._id]=fe.occurrences||0}),ie(te)}else console.error("Failed to load keywords:",K.message),window.toast(K.message||"Failed to load keywords","error"),w([]),ie({})}catch(o){console.error("Error loading keywords:",o),window.toast("Failed to load keywords","error"),w([]),ie({})}finally{j(!1)}},v=async()=>{if(!h||!A.trim()){window.toast("Please select a plugin and enter a keyword","error");return}try{const o=localStorage.getItem("adminToken"),z=t.find(se=>se.pluginSlug===h),K=await(await fetch("https://pluginsight.vercel.app/api/keywords",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:h,pluginName:(z==null?void 0:z.displayName)||h,keyword:A.trim()})})).json();K.success?(window.toast("Keyword added successfully","success"),N(""),i(""),U(!1),h===n&&x()):window.toast(K.message||"Failed to add keyword","error")}catch(o){console.error("Error adding keyword:",o),window.toast("Failed to add keyword","error")}},J=async()=>{try{T(!0),window.toast("Refreshing keyword ranks...","info");const o=localStorage.getItem("adminToken"),G=await(await fetch("https://pluginsight.vercel.app/api/keywords/refresh-ranks",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();G.success?(window.toast(G.message,"success"),await x()):window.toast(G.message||"Failed to refresh keyword ranks","error")}catch(o){console.error("Error refreshing keyword ranks:",o),window.toast("Failed to refresh keyword ranks","error")}finally{T(!1)}},Y=async()=>{try{const o=localStorage.getItem("adminToken"),z="https://pluginsight.vercel.app",W=Array.from(P),K=await(await fetch(`${z}/api/keywords/bulk-delete`,{method:"DELETE",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({keywordIds:W})})).json();K.success?(window.toast(`${W.length} keywords deleted successfully`,"success"),I(new Set),R(!1),x()):window.toast(K.message||"Failed to delete keywords","error")}catch(o){console.error("Error deleting keywords:",o),window.toast("Failed to delete keywords","error")}},Z=async()=>{if(p)try{const o=localStorage.getItem("adminToken"),G=await(await fetch(`https://pluginsight.vercel.app/api/keywords/${p}`,{method:"DELETE",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();if(G.success){window.toast("Keyword deleted successfully","success"),_(!1),k(null);const K=new Set(C);K.delete(p),g(K),x()}else window.toast(G.message||"Failed to delete keyword","error")}catch(o){console.error("Error deleting keyword:",o),window.toast("Failed to delete keyword","error")}},ke=async(o=!1)=>{try{V(!0);const z=localStorage.getItem("adminToken"),se=await(await fetch(`https://pluginsight.vercel.app/api/competitors${o?"?autoDiscover=true":""}`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();se.success&&(O(se.competitors),o&&se.competitors.length>0&&window.toast(`Discovered ${se.competitors.length} competitor plugins`,"success"))}catch(z){console.error("Error loading competitors:",z),window.toast("Failed to load competitors","error")}finally{V(!1)}},Se=async()=>{if(!ae.trim()){window.toast("Please enter a plugin slug","error");return}try{const o=localStorage.getItem("adminToken"),G=await(await fetch("https://pluginsight.vercel.app/api/competitors",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:ae.trim()})})).json();G.success?(window.toast("Competitor added successfully","success"),re(""),X(!1),ke()):window.toast(G.message||"Failed to add competitor","error")}catch(o){console.error("Error adding competitor:",o),window.toast("Failed to add competitor","error")}},Ne=async o=>{oe(o),he(!0),F(!0);try{const z=localStorage.getItem("adminToken"),K=await(await fetch(`https://pluginsight.vercel.app/api/keywords/ranks/${encodeURIComponent(o.keyword)}/${o.pluginSlug}?limit=30`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();K.success?m(K.rankHistory):(window.toast("Failed to load rank history","error"),m([]))}catch(z){console.error("Error loading rank history:",z),window.toast("Failed to load rank history","error"),m([])}finally{F(!1)}},H=async o=>{oe(o),ce(!0),l(!0);try{const W=`https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(o.keyword)}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`,K=await(await fetch(W)).json();if(K&&K.plugins){const se=K.plugins.filter(te=>te.slug!==o.pluginSlug).sort((te,fe)=>(fe.active_installs||0)-(te.active_installs||0)).slice(0,10).map(te=>({pluginName:te.name,pluginSlug:te.slug,activeInstalls:te.active_installs||0,rating:te.rating||0,numRatings:te.num_ratings||0,testedUpTo:te.tested||"N/A",lastUpdated:te.last_updated||"N/A",wordpressUrl:`https://wordpress.org/plugins/${te.slug}/`}));ee(se)}else window.toast("Failed to load related plugins","error"),ee([])}catch(z){console.error("Error loading related plugins:",z),window.toast("Failed to load related plugins","error"),ee([])}finally{l(!1)}};return a.useEffect(()=>{S()},[]),a.useEffect(()=>{x()},[n]),a.useEffect(()=>{s==="competitors"&&ke(!0)},[s]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6",children:e.jsx("div",{children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(be,{className:"h-6 w-6 text-blue-600 mr-2"}),"Keyword Analysis"]})})}),e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsxs("button",{onClick:()=>r("performance"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="performance"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(ve,{className:"h-4 w-4 inline mr-2"}),"Keyword Performance"]}),e.jsxs("button",{onClick:()=>r("competitors"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="competitors"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx($e,{className:"h-4 w-4 inline mr-2"}),"Competitors"]})]})})]}),s==="performance"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-stretch md:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Plugin:"}),e.jsxs("select",{value:n,onChange:o=>c(o.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"All plugins"}),t.map(o=>e.jsx("option",{value:o.pluginSlug,children:o.displayName},o.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[P.size>0&&e.jsxs("button",{onClick:()=>R(!0),className:"flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:[e.jsx(Te,{className:"h-4 w-4 mr-1"}),"Delete (",P.size,")"]}),e.jsxs("button",{onClick:()=>U(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Ae,{className:"h-4 w-4 mr-1"}),"Add"]}),e.jsxs("button",{onClick:J,disabled:b,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(le,{className:`h-4 w-4 mr-1 ${b?"animate-spin":""}`}),b?"Refreshing...":"Refresh Ranks"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-base font-semibold text-gray-900",children:["Keywords",n&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["for"," ",(pe=t.find(o=>o.pluginSlug===n))==null?void 0:pe.displayName]})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",d.length," keywords"]})})]}),$?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading keywords..."})]}):d.length>0?e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto max-h-[470px] overflow-y-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0 z-10",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12",children:e.jsx("input",{type:"checkbox",checked:d.every(o=>P.has(o._id))&&d.length>0,onChange:o=>{const z=new Set(P);o.target.checked?d.forEach(W=>z.add(W._id)):d.forEach(W=>z.delete(W._id)),I(z)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4",children:"Keyword"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Position"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Analytics"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Occurrences"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Tracked"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map((o,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-12",children:e.jsx("input",{type:"checkbox",checked:P.has(o._id),onChange:W=>{const G=new Set(P);W.target.checked?G.add(o._id):G.delete(o._id),I(G)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-1/4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.keyword})}),o.source&&e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${o.source==="manual"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:o.source})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:o.position||"-"}),o.rankChange!==null&&o.rankChange!==void 0&&e.jsxs("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${o.rankChange<0?"bg-green-100 text-green-800":o.rankChange>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[o.rankChange<0?"↑":o.rankChange>0?"↓":"=",Math.abs(o.rankChange)]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm w-20",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>Ne(o),className:"inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",title:"View Rank Analytics",children:e.jsx(ve,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>H(o),className:"inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors",title:"Search Related Plugins",children:e.jsx(Us,{className:"h-4 w-4"})})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20",children:ue[o._id]||0}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:o.addedAt?new Date(o.addedAt).toLocaleDateString("en-GB"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:o.updatedAt?new Date(o.updatedAt).toLocaleDateString("en-GB"):"-"})]},o._id))})]})})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(be,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Keywords Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:n?"No keywords added for this plugin yet.":"Please select a plugin first to view and manage keywords."}),n&&e.jsxs("button",{onClick:()=>U(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Add First Keyword"]})]})]})]}),s==="competitors"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-semibold text-gray-900",children:"Competitor Plugins"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>ke(!0),disabled:M,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(le,{className:`h-4 w-4 mr-1 ${M?"animate-spin":""}`}),M?"Discovering...":"Discover"]}),e.jsxs("button",{onClick:()=>X(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Ae,{className:"h-4 w-4 mr-1"}),"Add Manually"]})]})]}),M?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading competitors..."})]}):L.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Slug"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Rank"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:L.map((o,z)=>{var W;return e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:o.pluginName})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:o.pluginSlug})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.currentRank?`#${o.currentRank}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:((W=o.activeInstalls)==null?void 0:W.toLocaleString())||"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[o.tags&&o.tags.length>0?o.tags.slice(0,3).map((G,K)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:G},`${o._id}-tag-${K}`)):e.jsx("span",{className:"text-gray-400",children:"No tags"}),o.tags&&o.tags.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",o.tags.length-3," more"]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:new Date(o.createdAt).toLocaleDateString("en-GB")})]},o._id)})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx($e,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Competitors Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add keywords to automatically discover competitor plugins, or add competitors manually."}),e.jsxs("button",{onClick:()=>X(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Add First Competitor"]})]})]}),e.jsx(ye,{isOpen:y,onClose:()=>{U(!1),N(""),i("")},title:"Add New Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Plugin"}),e.jsxs("select",{value:h,onChange:o=>i(o.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Choose a plugin..."}),t.map(o=>e.jsx("option",{value:o.pluginSlug,children:o.displayName},o.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Keyword"}),e.jsx("input",{type:"text",value:A,onChange:o=>N(o.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter keyword...",onKeyDown:o=>{o.key==="Enter"&&v()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{U(!1),N(""),i("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:v,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Keyword"})]})]})}),e.jsx(ye,{isOpen:E,onClose:()=>R(!1),title:"Delete Keywords",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:["Are you sure you want to delete ",P.size," selected keyword(s)? This action cannot be undone and will also remove all related analytics data."]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>R(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Y,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keywords"})]})]})}),e.jsx(ye,{isOpen:D,onClose:()=>{_(!1),k(null)},title:"Delete Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this keyword? This action cannot be undone and will also remove all related analytics data."}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{_(!1),k(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Z,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keyword"})]})]})}),e.jsx(ye,{isOpen:Q,onClose:()=>{X(!1),re("")},title:"Add Competitor Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsx("input",{type:"text",value:ae,onChange:o=>re(o.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter plugin slug...",onKeyDown:o=>{o.key==="Enter"&&Se()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{X(!1),re("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Se,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Competitor"})]})]})}),e.jsx(ye,{isOpen:xe,onClose:()=>{he(!1),oe(null),m([])},title:`Rank Analytics - ${(ne==null?void 0:ne.keyword)||""}`,children:e.jsxs("div",{className:"space-y-4",children:[u?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading rank history..."})]}):ge.length>0?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Rank Trend (Last 30 days)"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Plugin:"})," ",ne==null?void 0:ne.pluginName]})]}),e.jsx("div",{className:"h-64",children:e.jsx(Pe,{width:"100%",height:"100%",children:e.jsxs(Je,{data:ge.slice().reverse().map(o=>({...o,displayDate:o.date,invertedRank:o.rank?-o.rank:0})),margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Ee,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(Le,{dataKey:"displayDate",tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"}}),e.jsx(_e,{domain:["dataMin","dataMax"],tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"},tickFormatter:o=>`#${Math.abs(o)}`}),e.jsx(Ce,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px"},formatter:o=>[`#${Math.abs(o)}`,"Rank"],labelFormatter:o=>`Date: ${o}`}),e.jsx(Ye,{type:"monotone",dataKey:"invertedRank",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},children:e.jsx(Ue,{dataKey:"invertedRank",position:"top",formatter:o=>`#${Math.abs(o)}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Fs,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Rank History"}),e.jsx("p",{className:"text-gray-600",children:"No rank history found for this keyword."})]}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx("button",{onClick:()=>{he(!1),oe(null),m([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})}),e.jsx(ye,{isOpen:je,onClose:()=>{ce(!1),oe(null),ee([])},title:`Related Plugins - "${(ne==null?void 0:ne.keyword)||""}"`,maxWidth:"max-w-6xl",fixedHeight:!0,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:q?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading related plugins..."})]}):B.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tested Up To"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:B.map((o,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap",children:[e.jsxs("a",{href:o.wordpressUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 font-medium flex items-center",title:hs(o.pluginName),children:[gt(o.pluginName),e.jsx(Oe,{className:"h-3 w-3 ml-1"})]}),e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:o.pluginSlug})]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.activeInstalls>0?o.activeInstalls.toLocaleString():"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Me,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsxs("span",{children:[o.rating>0?o.rating.toFixed(1):"N/A",o.numRatings>0&&e.jsxs("span",{className:"text-xs text-gray-400 ml-1",children:["(",o.numRatings,")"]})]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.testedUpTo!=="N/A"?`WP ${o.testedUpTo}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:o.lastUpdated!=="N/A"?(()=>{const W=new Date(o.lastUpdated);if(isNaN(W.getTime()))return"N/A";const G=W.getDate().toString().padStart(2,"0"),K=(W.getMonth()+1).toString().padStart(2,"0"),se=W.getFullYear();return`${K}-${G}-${se}`})():"N/A"})]},o.pluginSlug))})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(be,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Related Plugins Found"}),e.jsx("p",{className:"text-gray-600",children:"No plugins found containing this keyword."})]})}),e.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200 flex-shrink-0",children:e.jsx("button",{onClick:()=>{ce(!1),oe(null),ee([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})})]})},ft=()=>{const[s,r]=a.useState("downloaded-data"),[t,f]=a.useState([]),[n,c]=a.useState(!1),[d,w]=a.useState(""),[$,j]=a.useState([]),[y,U]=a.useState(!1),[A,N]=a.useState("15"),[h,i]=a.useState({start:"",end:""}),[b,T]=a.useState(""),[P,I]=a.useState([]),[E,R]=a.useState(!1),[C,g]=a.useState({startDate:"",endDate:"",rating:[],page:1}),[p,k]=a.useState({totalReviews:0,averageRating:0,ratingDistribution:{}});a.useEffect(()=>{D()},[]);const D=async()=>{try{c(!0);const L=localStorage.getItem("adminToken"),M=await fetch("https://pluginsight.vercel.app/api/analytics/added-plugins",{headers:{Authorization:`Bearer ${L}`,"Content-Type":"application/json"}});if(!M.ok)throw new Error(`HTTP error! status: ${M.status}`);const V=await M.json();V.success?f(V.plugins):console.warn("Failed to load added plugins:",V.message)}catch(L){console.error("Error loading added plugins:",L),L.name==="TypeError"&&L.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins","error")}finally{c(!1)}},_=[{id:"downloaded-data",name:"Downloaded Data",icon:me},{id:"plugin-reviews",name:`Plugin Reviews${p.totalReviews>0?` (${p.totalReviews})`:""}`,icon:Ve}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(ts,{className:"h-6 w-6 text-blue-600 mr-2"}),"Plugin Data Analysis"]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8 px-6",children:_.map(L=>{const O=L.icon;return e.jsxs("button",{onClick:()=>r(L.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${s===L.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(O,{className:"h-4 w-4"}),e.jsx("span",{children:L.name})]},L.id)})})}),e.jsxs("div",{className:"p-6",children:[s==="downloaded-data"&&e.jsx(yt,{addedPlugins:t,selectedPlugin:d,setSelectedPlugin:w,downloadData:$,setDownloadData:j,downloadLoading:y,setDownloadLoading:U,dateRange:A,setDateRange:N,customDateRange:h,setCustomDateRange:i}),s==="plugin-reviews"&&e.jsx(bt,{addedPlugins:t,reviewsPlugin:b,setReviewsPlugin:T,reviews:P,setReviews:I,reviewsLoading:E,setReviewsLoading:R,reviewFilters:C,setReviewFilters:g,reviewStats:p,setReviewStats:k})]})]})]})},yt=({addedPlugins:s,selectedPlugin:r,setSelectedPlugin:t,downloadData:f,setDownloadData:n,downloadLoading:c,setDownloadLoading:d,dateRange:w,setDateRange:$,customDateRange:j,setCustomDateRange:y})=>{var N;const U=async()=>{try{d(!0),window.toast("Starting plugin download data fetch...","info");const h=localStorage.getItem("adminToken"),T=await(await fetch("https://pluginsight.vercel.app/api/analytics/download-data/refresh",{method:"POST",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}})).json();T.success?(window.toast(T.message,"success"),r&&A(r)):window.toast(T.message||"Failed to refresh download data","error")}catch(h){console.error("Error refreshing download data:",h),window.toast("Failed to refresh download data","error")}finally{d(!1)}},A=async h=>{if(h)try{d(!0);const i=localStorage.getItem("adminToken"),b="https://pluginsight.vercel.app";let T=`${b}/api/analytics/download-data/${h}?days=${w}`;w==="custom"&&j.start&&j.end&&(T=`${b}/api/analytics/download-data/${h}?startDate=${j.start}&endDate=${j.end}`);const I=await(await fetch(T,{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();if(I.success){const E=I.downloadData.map(R=>({date:new Date(R.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:R.downloads,fullDate:R.date}));n(E)}}catch(i){console.error("Error loading download data:",i),window.toast("Failed to load download data","error")}finally{d(!1)}};return qe.useEffect(()=>{r&&A(r)},[r,w,j]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:r,onChange:h=>t(h.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(h=>e.jsx("option",{value:h.pluginSlug,children:h.displayName||h.pluginName},h.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:w,onChange:h=>$(h.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),w==="custom"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:j.start,onChange:h=>y(i=>({...i,start:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:j.end,onChange:h=>y(i=>({...i,end:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("button",{onClick:U,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(le,{className:`h-4 w-4 mr-2 ${c?"animate-spin":""}`}),"Refresh"]})]}),r&&f.length>0?e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Download Trends -"," ",((N=s.find(h=>h.pluginSlug===r))==null?void 0:N.displayName)||r]}),e.jsx("div",{className:"h-96",children:e.jsx(Pe,{width:"100%",height:"100%",children:e.jsxs(os,{data:f,children:[e.jsx(Ee,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(Le,{dataKey:"date",stroke:"#6b7280",fontSize:12}),e.jsx(_e,{stroke:"#6b7280",fontSize:12,tickFormatter:h=>h.toLocaleString()}),e.jsx(Ce,{contentStyle:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"8px"},formatter:h=>[h.toLocaleString(),"Downloads"],labelFormatter:(h,i)=>i&&i[0]?new Date(i[0].payload.fullDate).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):h}),e.jsx(is,{dataKey:"downloads",fill:"#3b82f6",radius:[4,4,0,0],children:e.jsx(Ue,{dataKey:"downloads",position:"top",fontSize:10,fill:"#3b82f6",formatter:h=>h.toLocaleString()})})]})})})]}):r&&c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading download data..."})]})}):r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(me,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Download Data"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No download data found for this plugin. Click refresh to fetch the latest data."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(me,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its download trends."})]})})]})},bt=({addedPlugins:s,reviewsPlugin:r,setReviewsPlugin:t,reviews:f,setReviews:n,reviewsLoading:c,setReviewsLoading:d,reviewFilters:w,setReviewFilters:$,reviewStats:j,setReviewStats:y})=>{const U=async()=>{try{d(!0),window.toast("Starting plugin reviews fetch...","info");const i=localStorage.getItem("adminToken"),T=await fetch("https://pluginsight.vercel.app/api/analytics/reviews/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const P=await T.json();P.success?(window.toast(P.message,"success"),r&&A(r)):window.toast(P.message||"Failed to refresh reviews","error")}catch(i){console.error("Error refreshing reviews:",i),i.name==="TypeError"&&i.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to refresh reviews","error")}finally{d(!1)}},A=async(i,b=1)=>{var T;if(i)try{d(!0);const P=new URLSearchParams({page:b.toString(),limit:"20"});w.startDate&&P.append("startDate",w.startDate),w.endDate&&P.append("endDate",w.endDate),w.rating.length>0&&w.rating.forEach(R=>P.append("rating",R.toString())),console.log(`Loading reviews for plugin: ${i} with params:`,P.toString());const I=await Ie(`/api/analytics/reviews/${i}?${P}`);if(!I.ok)throw new Error(`HTTP error! status: ${I.status}`);const E=await I.json();console.log("Reviews API response:",E),E.success?(console.log(`Loaded ${((T=E.reviews)==null?void 0:T.length)||0} reviews for ${i} (page ${b})`),n(b===1?E.reviews||[]:R=>[...R,...E.reviews||[]]),y(E.stats||{})):(console.warn("Reviews API returned success: false",E),b===1&&n([]),y({}))}catch(P){console.error("Error loading reviews:",P),P.name==="TypeError"&&P.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast(`Failed to load reviews: ${P.message}`,"error"),b===1&&(n([]),y({}))}finally{d(!1)}};qe.useEffect(()=>{r&&A(r)},[r,w]);const N=i=>Array.from({length:5},(b,T)=>e.jsx("span",{className:`text-lg ${T<i?"text-yellow-400":"text-gray-300"}`,children:"★"},T)),h=i=>{if(!i)return"";let b=i.replace(/<!\[CDATA\[/g,"").replace(/\]\]>/g,"").trim();b=b.replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/^.*?Replies:\s*\d+\s*/gi,"").replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*/gi,"").replace(/Rating:\s*\d+\s*stars?\s*/gi,"").trim();const T=document.createElement("textarea");return T.innerHTML=b,b=T.value,b=b.replace(/<[^>]*>/g,""),b=b.replace(/\s+/g," ").trim(),b};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:r,onChange:i=>t(i.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(i=>e.jsx("option",{value:i.pluginSlug,children:i.displayName||i.pluginName},i.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:w.startDate,onChange:i=>$(b=>({...b,startDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:w.endDate,onChange:i=>$(b=>({...b,endDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating Filter"}),e.jsxs("select",{value:w.rating.length===1?w.rating[0]:"",onChange:i=>{const b=i.target.value;$(b===""?T=>({...T,rating:[]}):T=>({...T,rating:[parseInt(b)]}))},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All ratings"}),e.jsx("option",{value:"5",children:"5 stars"}),e.jsx("option",{value:"4",children:"4 stars"}),e.jsx("option",{value:"3",children:"3 stars"}),e.jsx("option",{value:"2",children:"2 stars"}),e.jsx("option",{value:"1",children:"1 star"})]})]})]}),e.jsxs("button",{onClick:U,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(le,{className:`h-4 w-4 mr-2 ${c?"animate-spin":""}`}),"Refresh"]})]}),r&&f.length>0?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"text-sm text-gray-600 flex items-center gap-2",children:["Total",e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:j.totalReviews}),"Reviews"]})})})}),e.jsx("div",{className:"h-[460px] overflow-y-auto space-y-4",children:f.map((i,b)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 animate-fade-in-up",style:{animationDelay:`${b*100}ms`,animationFillMode:"both"},children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex",children:N(i.rating)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["by ",i.author," •"," ",new Date(i.date).toLocaleDateString("en-GB")]})]}),i.reviewUrl&&e.jsx("a",{href:i.reviewUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-blue-500 rounded-lg hover:text-blue-700 transition-all duration-200",children:e.jsx(Oe,{className:"h-4 w-4"})})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:i.title}),e.jsx("div",{className:"text-gray-700 leading-relaxed",children:h(i.content)})]},i._id||b))})]}):r&&c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(le,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):r?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Reviews Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews found for this plugin. Click refresh to fetch the latest reviews."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its reviews."})]})})]})},wt=()=>{const{user:s}=we(),[r,t]=a.useState([]),[f,n]=a.useState(!0),[c,d]=a.useState(""),[w,$]=a.useState(""),[j,y]=a.useState(""),[U,A]=a.useState(!1),[N,h]=a.useState(!1),[i,b]=a.useState(!1),[T,P]=a.useState(!1),[I,E]=a.useState(!1),[R,C]=a.useState(null),[g,p]=a.useState({current:1,pages:1,total:0,limit:10}),[k,D]=a.useState({name:"",email:"",password:"",role:"member"}),[_,L]=a.useState({newPassword:"",confirmPassword:""}),[O,M]=a.useState({canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1}),V=async(m=1,B="",ee="")=>{try{n(!0);const u=localStorage.getItem("adminToken");if(!u){d("Authentication token not found. Please login again.");return}const F=new URLSearchParams({page:m.toString(),limit:"10",...B&&{search:B},...ee&&{role:ee}}),l=await fetch(`https://pluginsight.vercel.app/api/users?${F}`,{headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"}});if(!l.ok){if(l.status===401){d("Authentication failed. Please login again."),localStorage.removeItem("adminToken");return}throw new Error(`HTTP error! status: ${l.status}`)}const S=await l.json();S.success?(t(S.users),p(S.pagination),d("")):d(S.message||"Failed to fetch users")}catch(u){console.error("Fetch users error:",u),u.name==="TypeError"&&u.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to fetch users. Please try again.")}finally{n(!1)}};a.useEffect(()=>{V()},[]);const Q=()=>{V(1,w,j)},X=async m=>{m.preventDefault();try{const B=localStorage.getItem("adminToken"),u=await fetch("https://pluginsight.vercel.app/api/users",{method:"POST",headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"},body:JSON.stringify(k)});if(!u.ok)throw new Error(`HTTP error! status: ${u.status}`);const F=await u.json();F.success?(A(!1),D({name:"",email:"",password:"",role:"member"}),V(g.current,w,j)):d(F.message||"Failed to create user")}catch(B){console.error("Add user error:",B),B.name==="TypeError"&&B.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to create user")}},ae=async m=>{m.preventDefault();try{const B=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}`,{method:"PUT",headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"},body:JSON.stringify({name:k.name,email:k.email,role:k.role,isActive:k.isActive})});if(!u.ok)throw new Error(`HTTP error! status: ${u.status}`);const F=await u.json();F.success?(h(!1),C(null),V(g.current,w,j)):d(F.message||"Failed to update user")}catch(B){console.error("Edit user error:",B),B.name==="TypeError"&&B.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to update user")}},re=m=>{if(s&&s._id===m._id){window.toast("You cannot delete your own account","error");return}C(m),P(!0)},ue=async()=>{if(R)try{const m=localStorage.getItem("adminToken"),ee=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}`,{method:"DELETE",headers:{Authorization:`Bearer ${m}`,"Content-Type":"application/json"}});if(!ee.ok)throw new Error(`HTTP error! status: ${ee.status}`);const u=await ee.json();u.success?(V(g.current,w,j),P(!1),C(null)):d(u.message||"Failed to delete user")}catch(m){console.error("Delete user error:",m),m.name==="TypeError"&&m.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to delete user")}},ie=async m=>{if(m.preventDefault(),_.newPassword!==_.confirmPassword){d("Passwords do not match");return}try{const B=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"},body:JSON.stringify({newPassword:_.newPassword})});if(!u.ok)throw new Error(`HTTP error! status: ${u.status}`);const F=await u.json();F.success?(b(!1),C(null),L({newPassword:"",confirmPassword:""}),alert("Password reset successfully")):d(F.message||"Failed to reset password")}catch(B){console.error("Reset password error:",B),B.name==="TypeError"&&B.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to reset password")}},xe=m=>{C(m),D({name:m.name,email:m.email,role:m.role,isActive:m.isActive}),h(!0)},he=m=>{C(m),L({newPassword:"",confirmPassword:""}),b(!0)},je=m=>{var B,ee,u,F;C(m),M({canAddPlugins:((B=m.permissions)==null?void 0:B.canAddPlugins)||!1,canDeletePlugins:((ee=m.permissions)==null?void 0:ee.canDeletePlugins)||!1,canAddKeywords:((u=m.permissions)==null?void 0:u.canAddKeywords)||!1,canAddUsers:((F=m.permissions)==null?void 0:F.canAddUsers)||!1}),E(!0)},ce=m=>{switch(m){case"superadmin":return"bg-red-100 text-red-800";case"admin":return"bg-blue-100 text-blue-800";case"member":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},ne=m=>s.role==="superadmin"||s.role==="admin"&&m.role==="member"?s._id!==m._id:!1,oe=m=>!(s.role==="admin"&&m.role==="superadmin"),ge=m=>s._id===m._id?!1:s.role==="superadmin"||s.role==="admin"&&m.role==="member";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center p-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Bs,{className:"h-6 w-6 text-blue-600 mr-2"}),"Team Members"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage system users and their permissions"})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex justify-between items-center",children:[e.jsx("div",{children:["admin","superadmin"].includes(s==null?void 0:s.role)&&e.jsxs("button",{onClick:()=>A(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(Ae,{className:"h-4 w-4"}),"Add User"]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative",children:[e.jsx(be,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:w,onChange:m=>$(m.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Is,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsxs("select",{value:j,onChange:m=>y(m.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"})]})]}),e.jsx("button",{onClick:Q,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Search"})]})]}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:c}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:f?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Loading users..."})]}):r.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx($e,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:r.filter(oe).map(m=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:m.name}),e.jsx("div",{className:"text-sm text-gray-500",children:m.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${ce(m.role)}`,children:m.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${m.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:m.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(m.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("div",{className:"flex items-center gap-2",children:ne(m)&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>xe(m),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:e.jsx(ns,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>he(m),className:"text-yellow-600 hover:text-yellow-900",title:"Reset Password",children:e.jsx(Ms,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>je(m),className:"text-purple-600 hover:text-purple-900",title:"Manage Permissions",children:e.jsx(We,{className:"h-4 w-4"})}),ge(m)&&e.jsx("button",{onClick:()=>re(m),className:"text-red-600 hover:text-red-900",title:"Delete User",children:e.jsx(Te,{className:"h-4 w-4"})})]})})})]},m._id))})]})})}),g.pages>1&&e.jsxs("div",{className:"flex justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>V(g.current-1,w,j),disabled:g.current===1,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",g.current," of ",g.pages]}),e.jsx("button",{onClick:()=>V(g.current+1,w,j),disabled:g.current===g.pages,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Next"})]}),U&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Add New User"}),e.jsxs("form",{onSubmit:X,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:k.name,onChange:m=>D({...k,name:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:k.email,onChange:m=>D({...k,email:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{type:"password",value:k.password,onChange:m=>D({...k,password:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:k.role,onChange:m=>D({...k,role:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{A(!1),D({name:"",email:"",password:"",role:"member"})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Add User"})]})]})]})}),N&&R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Edit User"}),e.jsxs("form",{onSubmit:ae,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:k.name,onChange:m=>D({...k,name:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:k.email,onChange:m=>D({...k,email:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:k.role,onChange:m=>D({...k,role:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:(s==null?void 0:s.role)==="admin"&&["admin","superadmin"].includes(R.role),children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:k.isActive,onChange:m=>D({...k,isActive:m.target.checked}),className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{h(!1),C(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Update User"})]})]})]})}),i&&R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Reset Password"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Reset password for: ",e.jsx("strong",{children:R.name})]}),e.jsxs("form",{onSubmit:ie,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx("input",{type:"password",value:_.newPassword,onChange:m=>L({..._,newPassword:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",value:_.confirmPassword,onChange:m=>L({..._,confirmPassword:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{b(!1),C(null),L({newPassword:"",confirmPassword:""})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Reset Password"})]})]})]})}),T&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Os,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Delete User"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user? This action cannot be undone."})]})]}),R&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:R.name}),e.jsx("p",{className:"text-sm text-gray-500",children:R.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${ce(R.role)}`,children:R.role})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{P(!1),C(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:ue,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete User"})]})]})}),e.jsx(ye,{isOpen:I,onClose:()=>{E(!1),C(null)},title:"Manage User Permissions",children:R&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:R.name}),e.jsx("p",{className:"text-sm text-gray-500",children:R.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${ce(R.role)}`,children:R.role})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Plugins"}),e.jsx("input",{type:"checkbox",checked:O.canAddPlugins,onChange:m=>M(B=>({...B,canAddPlugins:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Delete Plugins"}),e.jsx("input",{type:"checkbox",checked:O.canDeletePlugins,onChange:m=>M(B=>({...B,canDeletePlugins:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Keywords"}),e.jsx("input",{type:"checkbox",checked:O.canAddKeywords,onChange:m=>M(B=>({...B,canAddKeywords:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Users"}),e.jsx("input",{type:"checkbox",checked:O.canAddUsers,onChange:m=>M(B=>({...B,canAddUsers:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>{E(!1),C(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>{window.toast("Permissions updated successfully","success"),E(!1),C(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Save Permissions"})]})]})})]})},jt=()=>{const{user:s}=we(),[r,t]=a.useState(!1),[f,n]=a.useState(""),[c,d]=a.useState(""),[w,$]=a.useState({member:{canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1},admin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0},superadmin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0}});a.useEffect(()=>{j()},[]);const j=async()=>{try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();P.success&&$(P.permissions)}catch(i){console.error("Error loading permissions:",i)}},y=(i,b,T)=>{$(P=>({...P,[i]:{...P[i],[b]:T}}))},U=async()=>{t(!0),d(""),n("");try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify({permissions:w})})).json();P.success?(n("Permissions updated successfully"),setTimeout(()=>n(""),3e3)):d(P.message||"Failed to update permissions")}catch(i){console.error("Error saving permissions:",i),d("Failed to update permissions")}finally{t(!1)}},A={canAddPlugins:"Add Plugins",canDeletePlugins:"Delete Plugins",canAddKeywords:"Add Keywords",canAddUsers:"Add Users"},N={member:"Member",admin:"Admin",superadmin:"Super Admin"};if(!s)return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-white rounded-lg p-8 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Be,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600",children:"Please login to access settings."})]})})});const h=["admin","superadmin"].includes(s.role);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(We,{className:"h-8 w-8 text-blue-600 mr-3"}),"Settings"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Manage application settings and user permissions"})]})})}),f&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:f}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:c}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 w-1/2",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx($e,{className:"h-5 w-5 text-gray-600 mr-2"}),"User Permissions"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Configure what actions each user role can perform"})]}),e.jsx("div",{className:"flex justify-end",children:h?e.jsxs("button",{onClick:U,disabled:r,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[e.jsx(ls,{className:"h-4 w-4 mr-2"}),r?"Saving...":"Save"]}):e.jsx("div",{className:"text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg",children:"View Only - Admin privileges required to edit"})})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Permission"}),Object.keys(N).map(i=>e.jsx("th",{className:"text-center py-3 px-4 font-medium text-gray-900",children:N[i]},i))]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:Object.keys(A).map(i=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-4 px-4 text-sm font-medium text-gray-900",children:A[i]}),Object.keys(N).map(b=>{var T;return e.jsx("td",{className:"py-4 px-4 text-center",children:e.jsx("input",{type:"checkbox",checked:((T=w[b])==null?void 0:T[i])||!1,onChange:P=>y(b,i,P.target.checked),disabled:!h||b==="superadmin",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"})},b)})]},i))})]})})})]})]})},Nt=()=>{const{user:s}=we(),[r,t]=a.useState(!1),[f,n]=a.useState(!1),[c,d]=a.useState({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),w=async A=>{A.preventDefault(),n(!0);try{if(c.newPassword){if(c.newPassword!==c.confirmPassword){window.toast("New passwords do not match","error"),n(!1);return}if(c.newPassword.length<6){window.toast("Password must be at least 6 characters","error"),n(!1);return}}const N=localStorage.getItem("adminToken"),h="https://pluginsight.vercel.app",i={name:c.name,email:c.email},b=await fetch(`${h}/api/users/${s.id||s._id}`,{method:"PUT",headers:{Authorization:`Bearer ${N}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!b.ok)throw new Error(`HTTP error! status: ${b.status}`);const T=await b.json();if(!T.success){window.toast(T.message||"Failed to update profile","error"),n(!1);return}if(c.newPassword){const I={newPassword:c.newPassword},E=await fetch(`${h}/api/users/${s.id||s._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${N}`,"Content-Type":"application/json"},body:JSON.stringify(I)});if(!E.ok)throw new Error(`HTTP error! status: ${E.status}`);const R=await E.json();if(!R.success){window.toast(R.message||"Failed to update password","error"),n(!1);return}}const P={...s,...T.user};localStorage.setItem("adminUser",JSON.stringify(P)),window.toast("Profile updated successfully","success"),t(!1),d(I=>({...I,name:P.name,email:P.email,newPassword:"",confirmPassword:""})),window.location.reload()}catch(N){console.error("Error updating profile:",N),N.name==="TypeError"&&N.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to update profile","error")}finally{n(!1)}},$=()=>{d({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),t(!1)},j=A=>{d({...c,[A.target.name]:A.target.value})},y=A=>{switch(A){case"superadmin":return e.jsx(Be,{className:"h-5 w-5 text-yellow-600"});case"admin":return e.jsx(Be,{className:"h-5 w-5 text-blue-600"});default:return e.jsx(Re,{className:"h-5 w-5 text-gray-600"})}},U=A=>{const N={superadmin:"bg-yellow-100 text-yellow-800 border-yellow-200",admin:"bg-blue-100 text-blue-800 border-blue-200",member:"bg-gray-100 text-gray-800 border-gray-200"};return e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${N[A]}`,children:[y(A),e.jsx("span",{className:"ml-2 capitalize",children:A})]})};return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your account information and preferences"})]}),e.jsx("div",{className:"",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-24 w-24 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4",children:e.jsx(Re,{className:"h-12 w-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:s==null?void 0:s.name}),e.jsx("p",{className:"text-blue-100 mb-4",children:s==null?void 0:s.email}),U(s==null?void 0:s.role)]})})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),r?e.jsxs("button",{onClick:$,className:"flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"Cancel"})]}):e.jsxs("button",{onClick:()=>t(!0),className:"flex items-center space-x-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:[e.jsx(ns,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Re,{className:"h-4 w-4 mr-2"}),"Full Name"]}),e.jsx("input",{type:"text",name:"name",value:c.name,onChange:j,disabled:!r,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(as,{className:"h-4 w-4 mr-2"}),"Email Address"]}),e.jsx("input",{type:"email",name:"email",value:c.email,onChange:j,disabled:!r,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Be,{className:"h-4 w-4 mr-2"}),"Role"]}),e.jsx("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",children:e.jsx("span",{className:"capitalize",children:s==null?void 0:s.role})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Role can only be changed by administrators"})]}),r&&e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsx("input",{type:"password",name:"newPassword",value:c.newPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",value:c.confirmPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirm new password"})]})]})]}),r&&e.jsx("div",{className:"flex justify-end pt-6",children:e.jsxs("button",{type:"submit",disabled:f,className:"flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ls,{className:"h-4 w-4"}),e.jsx("span",{children:f?"Saving...":"Save"})]})})]})]})})]})})})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})})},vt=["p","br","strong","b","em","i","u","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","pre","code","a","img","div","span","table","thead","tbody","tr","td","th","iframe","video","source"],kt={a:["href","title","target","rel"],img:["src","alt","title","width","height","class"],iframe:["src","width","height","frameborder","allowfullscreen","title","class"],video:["src","width","height","controls","autoplay","muted","loop","poster","class"],source:["src","type"],div:["class","id"],span:["class","id"],p:["class"],h1:["class"],h2:["class"],h3:["class"],h4:["class"],h5:["class"],h6:["class"],ul:["class"],ol:["class"],li:["class"],table:["class"],thead:["class"],tbody:["class"],tr:["class"],td:["class"],th:["class"],blockquote:["class"],pre:["class"],code:["class"]};function St(s){if(!s)return"";const r=document.createElement("div");r.innerHTML=s;function t(n){const c=n.tagName.toLowerCase();if(!vt.includes(c)){n.remove();return}if(c==="iframe"){const j=n.getAttribute("src"),y=n.getAttribute("title")||"Video content",U=document.createElement("div");U.className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";let A="Video",N=`<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;j&&j.includes("youtube")?(A="YouTube Video",N=`<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`):j&&j.includes("vimeo")&&(A="Vimeo Video",N=`<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`),U.innerHTML=`
        <div class="flex flex-col items-center space-y-3">
          ${N}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${A}</h4>
            <p class="text-gray-600 text-sm mb-3">${y}</p>
            <a href="${j}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `,n.parentNode.replaceChild(U,n);return}const d=kt[c]||[];Array.from(n.attributes).forEach(j=>{d.includes(j.name)||n.removeAttribute(j.name)}),Array.from(n.children).forEach(j=>t(j))}return Array.from(r.children).forEach(n=>t(n)),r.innerHTML}function Fe(s){if(!s)return"";let r=s.replace(/\[video\s+([^\]]+)\]/g,(t,f)=>{const n=f.match(/src="([^"]+)"/);return n?`<video controls><source src="${n[1]}" type="video/mp4"></video>`:""}).replace(/\[youtube\s+([^\]]+)\]/g,(t,f)=>{const n=f.match(/(?:id="|v=)([^"&\s]+)/);return n?`<iframe src="https://www.youtube.com/embed/${n[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`:""}).replace(/\[vimeo\s+([^\]]+)\]/g,(t,f)=>{const n=f.match(/id="?([^"\s]+)"?/);return n?`<iframe src="https://player.vimeo.com/video/${n[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`:""}).replace(/https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,(t,f)=>`<iframe src="https://www.youtube.com/embed/${f}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,(t,f)=>`<iframe src="https://www.youtube.com/embed/${f}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g,(t,f)=>`<iframe src="https://player.vimeo.com/video/${f}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`);return St(r)}const At=()=>{const{slug:s}=bs(),r=He(),[t,f]=a.useState(null),[n,c]=a.useState(null),[d,w]=a.useState(!0),[$,j]=a.useState(""),[y,U]=a.useState({}),[A,N]=a.useState(""),[h,i]=a.useState(!0),[b,T]=a.useState(!1),P=async()=>{try{i(!0);const p=await fetch(`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&slug=${t.slug}`);if(!p.ok)throw new Error("Failed to fetch plugin information");const k=await p.json();k.versions&&U(k.versions)}catch(p){console.error("Error fetching versions data:",p),U({})}finally{i(!1)}};a.useEffect(()=>{t&&P()},[t]),a.useEffect(()=>{I()},[s]);const I=async()=>{try{w(!0);const p=localStorage.getItem("adminToken"),k="https://pluginsight.vercel.app",[D,_]=await Promise.all([fetch(`${k}/api/plugins/${s}`,{headers:{Authorization:`Bearer ${p}`}}),fetch(`${k}/api/analytics/plugin-info/${s}`,{headers:{Authorization:`Bearer ${p}`}})]);if(!D.ok)throw new Error("Failed to fetch plugin details");const L=await D.json();if(!L.success){j(L.message||"Plugin not found");return}if(f(L.plugin),_.ok){const O=await _.json();O.success&&O.pluginInfo?c(O.pluginInfo):console.log("No plugin information found in database for:",s)}else console.log("Failed to fetch plugin information from database")}catch(p){console.error("Error fetching plugin details:",p),j("Failed to load plugin details")}finally{w(!1)}},E=p=>{if(!p)return"N/A";const k=p.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!k)return"N/A";const[,D,_,L]=k;return`${L}-${_}-${D}`},R=p=>{const k=Math.round((p||0)/20);return[...Array(5)].map((D,_)=>e.jsx(Me,{className:`h-4 w-4 ${_<k?"text-yellow-400 fill-current":"text-gray-300"}`},_))},C=p=>n&&n[p]!==void 0&&n[p]!==null?n[p]:t!=null&&t.pluginData&&t.pluginData[p]!==void 0&&t.pluginData[p]!==null?t.pluginData[p]:null,g=p=>{const k=p.split(".");if(n){let D=n;for(const _ of k)if(D&&typeof D=="object"&&D[_]!==void 0)D=D[_];else{D=null;break}if(D!==null)return D}if(t!=null&&t.pluginData){let D=t.pluginData;for(const _ of k)if(D&&typeof D=="object"&&D[_]!==void 0)D=D[_];else{D=null;break}return D}return null};return d?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):$||!t?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-600 text-xl mb-4",children:$}),e.jsx("button",{onClick:()=>r(-1),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{onClick:()=>r(-1),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(Hs,{className:"h-5 w-5"}),e.jsx("span",{children:"Back"})]}),e.jsx("div",{className:"h-6 w-px bg-gray-300"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Plugin Details"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[C("homepage")&&e.jsxs("button",{onClick:()=>window.open(C("homepage"),"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Oe,{className:"h-4 w-4"}),e.jsx("span",{children:"Home page"})]}),e.jsxs("button",{onClick:()=>window.open(`https://wordpress.org/plugins/${t.slug}/`,"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Oe,{className:"h-4 w-4"}),e.jsx("span",{children:"View on WordPress.org"})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[g("icons.2x")&&e.jsx("img",{src:g("icons.2x"),alt:t.name,className:"w-16 h-16 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:t.name}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[R(C("rating")),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",C("num_ratings")||0," ratings)"]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[e.jsx(me,{className:"h-4 w-4"}),e.jsxs("span",{children:[(C("downloaded")||0).toLocaleString()," ","downloads"]})]})]})]})]})}),g("sections.description")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Description"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Fe(g("sections.description"))}})]}),g("sections.installation")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Installation"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Fe(g("sections.installation"))}})]}),g("sections.faq")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequently Asked Questions"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Fe(g("sections.faq"))}})]}),g("sections.changelog")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Changelog"}),e.jsx("div",{className:"prose max-w-none text-gray-700 max-h-96 overflow-y-auto",dangerouslySetInnerHTML:{__html:Fe(g("sections.changelog"))}})]}),C("screenshots")&&Object.keys(C("screenshots")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Screenshots"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(C("screenshots")).slice(0,6).map(([p,k])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:k.src,alt:k.caption||`Screenshot ${p}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200",loading:"lazy"}),k.caption&&e.jsx("p",{className:"text-sm text-gray-600",children:k.caption})]},p))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plugin Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:C("version")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Rank"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",t.currentRank||"N/A"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:E(C("last_updated"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Added"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:E(C("added"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requires WP"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:C("requires")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tested up to"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:C("tested")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"PHP Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:C("requires_php")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Installs"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:C("active_installs")?`${C("active_installs").toLocaleString()}+`:"N/A"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Download"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 flex gap-1 items-center",children:["Current Version",e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Latest"})]}),e.jsxs("div",{className:"text-md font-bold text-gray-900",children:["v",C("version")||"N/A"]}),e.jsx("button",{onClick:()=>window.open(C("download_link"),"_blank"),className:"bg-green-600 hover:bg-green-700 text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:e.jsx(me,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Older Versions"}),h?e.jsx("div",{className:"border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-500",children:"Loading versions..."}):e.jsxs("select",{value:A,onChange:p=>N(p.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a version..."}),Object.entries(y).filter(([p])=>{var k;return p!==((k=t==null?void 0:t.pluginData)==null?void 0:k.version)}).sort((p,k)=>{const D=O=>O.split(".").map(Number),[_,L]=[D(p[0]),D(k[0])];for(let O=0;O<Math.max(_.length,L.length);O++){const M=(L[O]||0)-(_[O]||0);if(M!==0)return M}return 0}).slice(0,15).map(([p])=>e.jsxs("option",{value:p,children:["v",p]},p))]}),e.jsx("button",{onClick:p=>{p.preventDefault(),A&&(y!=null&&y[A])&&(T(!0),window.open(y[A],"_blank"),setTimeout(()=>T(!1),2e3))},disabled:!A||b||h,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:b?e.jsx(e.Fragment,{children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"})}):e.jsx(e.Fragment,{children:e.jsx(me,{className:"h-4 w-4"})})})]})})]}),C("donate_link")&&e.jsxs("button",{onClick:()=>window.open(C("donate_link"),"_blank"),className:"w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[e.jsx(zs,{className:"h-4 w-4"}),e.jsx("span",{children:"Donate"})]})]}),C("author")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Author"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Re,{className:"h-8 w-8 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:C("author").replace(/<[^>]*>/g,"")}),C("author_profile")&&e.jsx("a",{href:C("author_profile"),target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800",children:"View Profile"})]})]})]}),C("tags")&&Object.keys(C("tags")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.keys(C("tags")).slice(0,10).map(p=>e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:p},p))})]}),C("contributors")&&Object.keys(C("contributors")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contributors"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:Object.entries(C("contributors")).slice(0,10).map(([p,k])=>e.jsx("a",{href:k.profile,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 rounded-lg shadow-sm",children:e.jsx("img",{src:k.avatar,alt:k.display_name||p,title:k.display_name||p,className:"w-8 h-8 rounded-full"})},p))})]})]})]})})]})};function Pt(){return e.jsx(tt,{children:e.jsx(et,{children:e.jsx(ws,{children:e.jsxs(js,{children:[e.jsx(de,{path:"/login",element:e.jsx(it,{})}),e.jsxs(de,{path:"/*",element:e.jsx(at,{children:e.jsx(ot,{})}),children:[e.jsx(de,{path:"dashboard",element:e.jsx(xt,{})})," ",e.jsx(de,{path:"plugin-rank",element:e.jsx(ht,{})}),e.jsx(de,{path:"keyword-analysis",element:e.jsx(pt,{})}),e.jsx(de,{path:"analytics",element:e.jsx(ft,{})}),e.jsx(de,{path:"users",element:e.jsx(wt,{})}),e.jsx(de,{path:"settings",element:e.jsx(jt,{})}),e.jsx(de,{path:"profile",element:e.jsx(Nt,{})}),e.jsx(de,{path:"plugin-details/:slug",element:e.jsx(At,{})}),e.jsx(de,{path:"",element:e.jsx(ss,{to:"/dashboard",replace:!0})})]})]})})})})}ms(document.getElementById("root")).render(e.jsx(a.StrictMode,{children:e.jsx(Pt,{})}));
